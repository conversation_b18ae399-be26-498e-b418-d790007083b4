<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="user-id" content="{{ current_user.id }}">
    <meta name="username" content="{{ current_user.username }}">
    <title>Kevko Systems Dashboard</title>

    <!-- Favicon links -->
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ url_for('static', filename='favicon-16x16.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ url_for('static', filename='favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="96x96" href="{{ url_for('static', filename='favicon-96x96.png') }}">
    <link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}">

    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- D3.js for world map visualization -->
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://d3js.org/topojson.v3.min.js"></script>
    <!-- Service Framework Scripts -->
    <link rel="stylesheet" href="/static/css/dashboard.css">
    <link rel="stylesheet" href="/static/css/access-control.css">
    <link rel="stylesheet" href="/static/css/statistics-modern.css">
    <link rel="stylesheet" href="/static/css/group-chat.css">
    <link rel="stylesheet" href="/static/css/group-members.css">
    <link rel="stylesheet" href="/static/css/admin-updates.css">
    <link rel="stylesheet" href="/static/css/profile.css">
    <link rel="stylesheet" href="/static/css/friends-panel.css">
    <link rel="stylesheet" href="/static/css/chat-grid.css">

    <style>
        /* Additional Theme Modal Styling */
        .modal-open {
            overflow: hidden;
            backdrop-filter: blur(4px);
        }

        .modal-content {
            transition: all 0.3s ease;
        }

        /* Mobile search styling */
        @media (max-width: 767px) {
            #searchContainer {
                transition: width 0.3s ease;
            }

            #searchContainer.w-36 {
                width: 9rem;
            }

            #globalSearchInput:not(.hidden) {
                width: 100px;
                display: block;
                margin-left: 4px;
                padding: 0;
                background: transparent;
                border: none;
            }
        }
    </style>
    <script src="/static/js/ServiceManager.js"></script>
    <script src="/static/js/services/kevkoHome/index.js"></script>
    <script src="/static/js/services/kevkoFy/index.js"></script>
    <script src="/static/js/services/kevkoWeather/index.js"></script>
    <script src="/static/js/services/kevkoNotes/index.js"></script>
    <script src="/static/js/services/kevkoCloud/index.js"></script>
    <script src="/static/js/services/kevkoAI/index.js"></script>
    <script src="/static/js/services/config.js"></script>
    <script src="/static/js/ServiceEditor.js"></script>
    <script src="/static/js/UserManager.js"></script>
    <script src="/static/js/ServiceRestrictions.js"></script>
    <script src="/static/js/SettingsManager.js"></script>
    <script src="/static/js/Dashboard.js"></script>
    <script src="/static/js/ServiceEngagementTracker.js"></script>
    <!-- Load NotificationManager after other scripts -->
    <script src="/static/js/NotificationManager.js"></script>
</head>
<body class="dark bg-gradient min-h-screen">
    <!-- Background particle effect canvas -->
    <canvas id="particleCanvas" class="absolute inset-0 w-full h-full opacity-30"></canvas>

    <!-- Notification Container -->
    <div id="notification-container" class="fixed top-4 right-4 z-50 flex flex-col items-end space-y-2"></div>

    <!-- Mobile sidebar overlay -->
    <div class="sidebar-overlay hidden"></div>

    <div class="max-w-[1800px] mx-auto px-4 sm:px-6 md:px-8 lg:px-16 pt-2 pb-8 relative z-10">
        <!-- Header -->
        <header class="flex items-center justify-between py-3 sm:py-4 border-b border-slate-700/50 mb-4 sm:mb-6">
            <div class="flex items-center space-x-2">
                <!-- Mobile menu toggle button (only visible on mobile) -->
                <button id="mobileMenuToggle" class="mobile-menu-toggle md:hidden">
                    <i data-lucide="menu" class="h-5 w-5"></i>
                </button>

                <i data-lucide="hexagon" class="h-6 w-6 sm:h-8 sm:w-8 text-cyan-500"></i>
                <span class="text-lg sm:text-xl font-bold bg-gradient-to-r from-cyan-400 to-purple-500 bg-clip-text text-transparent">
                    Kevko Systems
                </span>
            </div>

            <div class="flex items-center space-x-6">
                <!-- Search Bar -->
                <div class="flex items-center space-x-1 bg-slate-800/50 rounded-full px-2 py-1.5 md:px-3 border border-slate-700/50 backdrop-blur-sm transition-all duration-300" id="searchContainer">
                    <button type="button" id="mobileSearchIcon" class="flex items-center justify-center focus:outline-none">
                        <i data-lucide="search" class="h-4 w-4 text-slate-400"></i>
                    </button>
                    <input type="text" id="globalSearchInput" placeholder="Search..."
                           class="bg-transparent border-none focus:outline-none text-sm w-40 placeholder:text-slate-500 hidden md:block transition-all duration-300">
                </div>
                <!-- Global Search Results Container -->
                <div id="globalSearchResults" class="hidden absolute top-16 right-4 w-96 max-h-[70vh] overflow-y-auto bg-slate-800 border border-slate-700 rounded-lg shadow-xl z-50 p-4 space-y-4">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-slate-100">Search Results</h3>
                        <button id="closeGlobalSearch" class="text-slate-400 hover:text-slate-100">
                            <i data-lucide="x" class="h-5 w-5"></i>
                        </button>
                    </div>
                    <div id="globalSearchContent" class="space-y-4">
                        <!-- Results will be populated here -->
                    </div>
                </div>

                <!-- Theme Toggle -->
                <button id="themeToggle" class="text-slate-400 hover:text-slate-100">
                    <i data-lucide="moon" class="h-5 w-5"></i>
                </button>

                <!-- User Menu -->
                <div class="relative">
                    <div id="userMenuTrigger" class="cursor-pointer">
                        <div class="w-10 h-10 rounded-full bg-slate-700 flex items-center justify-center text-white font-medium">
                            <!-- User initials will be populated by JS -->
                        </div>
                    </div>

                    <div id="userMenu" class="user-menu hidden">
                        <div class="p-3 border-b border-slate-700">
                            <p class="text-sm font-medium text-slate-200"><!-- Username will be populated by JS --></p>
                            <p class="text-xs text-slate-400"><!-- Email will be populated by JS --></p>
                        </div>
                        <div class="p-2">
                            <a href="#" class="block w-full text-left px-2 py-1 text-sm text-slate-300 hover:bg-slate-700 hover:text-slate-100 rounded" id="profileLink" data-view="profile">
                                <i data-lucide="user" class="h-4 w-4 inline mr-2"></i>
                                Profile
                            </a>
                            <a href="#" id="adminLink" class="block w-full text-left px-2 py-1 text-sm text-slate-300 hover:bg-slate-700 hover:text-slate-100 rounded hidden">
                                <i data-lucide="shield" class="h-4 w-4 inline mr-2"></i>
                                Admin
                            </a>
                            <hr class="my-1 border-slate-700">
                            <a href="/auth/logout" class="block w-full text-left px-2 py-1 text-sm text-slate-300 hover:bg-slate-700 hover:text-slate-100 rounded">
                                <i data-lucide="log-out" class="h-4 w-4 inline mr-2"></i>
                                Sign out
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main content with sidebar -->
        <div class="grid grid-cols-12 gap-6">
            <!-- Sidebar - Desktop (visible on md and up) -->
            <div class="sidebar-container col-span-12 md:col-span-3 lg:col-span-2 hidden md:block">
                <div class="card p-4">
                    <nav class="space-y-2">
                        <a href="#" class="nav-item active" data-view="dashboard">
                            <i data-lucide="command" class="h-4 w-4 mr-2"></i>
                            Dashboard
                        </a>
                        <a href="#" class="nav-item" data-view="store">
                            <i data-lucide="shopping-bag" class="h-4 w-4 mr-2"></i>
                            Store
                        </a>
                        <a href="#" class="nav-item" data-view="profile">
                            <i data-lucide="user" class="h-4 w-4 mr-2"></i>
                            Profile
                        </a>
                        <a href="#" class="nav-item" data-view="friends">
                            <i data-lucide="message-circle" class="h-4 w-4 mr-2"></i>
                            Friends
                        </a>
                        <a href="#" class="nav-item" data-view="settings">
                            <i data-lucide="settings" class="h-4 w-4 mr-2"></i>
                            Settings
                        </a>
                        <hr class="my-4 border-slate-700/50">
                        <a href="#" class="nav-item" data-view="contact">
                            <i data-lucide="phone" class="h-4 w-4 mr-2"></i>
                            Contact
                        </a>
                        <a href="#" class="nav-item hidden" data-view="statistics">
                            <i data-lucide="bar-chart-2" class="h-4 w-4 mr-2"></i>
                            Statistics
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main content -->
            <div class="col-span-12 md:col-span-9 lg:col-span-10">
                <!-- Dashboard View -->
                <div id="dashboardView" class="view-content">
                    <div class="card p-6 mb-6">
                        <div class="relative z-10">
                            <h1 class="text-2xl font-bold text-slate-100 mb-2">Welcome to Kevko Systems</h1>
                            <p class="text-slate-400 max-w-2xl">
                                Your central hub for all Kevko services. Access your favorite applications and stay updated with the latest features.
                            </p>
                            <div class="flex items-center mt-4 text-sm text-slate-500">
                                <i data-lucide="clock" class="h-4 w-4 mr-1"></i>
                                <span id="currentTime"></span>
                                <span class="mx-2">•</span>
                                <i data-lucide="calendar" class="h-4 w-4 mr-1"></i>
                                <span id="currentDate"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Services Grid -->
                    <div id="servicesContainer" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
                        <!-- Services will be dynamically loaded here -->
                    </div>

                    <!-- Updates Section -->
                    <div class="card p-6">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex items-center">
                                <i data-lucide="refresh-cw" class="h-5 w-5 text-cyan-500 mr-2"></i>
                                <h2 class="text-lg font-medium text-slate-100">Recent Updates</h2>
                            </div>
                            <button id="toggleUpdates" class="text-sm text-slate-400 hover:text-slate-100 flex items-center">
                                <span id="toggleText">Show All</span>
                                <i data-lucide="chevron-down" class="h-4 w-4 ml-1" id="toggleIcon"></i>
                            </button>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4" id="updatesContainer">
                            <!-- Updates werden dynamisch eingefügt -->
                        </div>
                    </div>
                </div>

                <!-- Store View -->
                <div id="storeView" class="view-content hidden">

                    <div class="card p-6">
                        <div class="flex items-center mb-6">
                            <i data-lucide="shopping-bag" class="h-6 w-6 text-cyan-500 mr-2"></i>
                            <h2 class="text-xl font-bold text-slate-100">Service Store</h2>
                        </div>
                        <div class="mb-6">
                            <div id="storeInfoMessage" class="bg-slate-800/50 rounded-lg p-4 border border-slate-700/50 mb-4">
                                <div class="flex items-center">
                                    <div class="p-3 rounded-lg bg-cyan-500/10 mr-4">
                                        <i data-lucide="info" class="h-6 w-6 text-cyan-500"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-md font-medium text-slate-200 mb-1">Add Services to Your Dashboard</h3>
                                        <p class="text-sm text-slate-400">Your dashboard starts empty. Browse and install services from the Store to add them to your dashboard. Click the "Install" button on any service to add it.</p>
                                    </div>
                                </div>
                            </div>
                            <p class="text-slate-400 mb-4">
                                Browse and install services for your dashboard. Select the services you want to use.
                            </p>
                            <div class="flex items-center space-x-2 mb-4">
                                <div class="relative">
                                    <input type="text" id="storeSearch" placeholder="Search services..." class="w-full bg-slate-800/50 border border-slate-700/50 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500 pl-10">
                                    <i data-lucide="search" class="h-4 w-4 text-slate-400 absolute left-3 top-2.5"></i>
                                </div>
                                <select id="storeCategory" class="bg-slate-800/50 border border-slate-700/50 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                                    <option value="all">All Categories</option>
                                    <option value="Entertainment">Entertainment</option>
                                    <option value="Productivity">Productivity</option>
                                    <option value="Smart Home">Smart Home</option>
                                    <option value="Storage">Storage</option>
                                    <option value="Utilities">Utilities</option>
                                </select>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="storeContainer">
                            <!-- Store items will be dynamically loaded here -->
                        </div>
                    </div>
                </div>



                <!-- Settings View -->
                <div id="settingsView" class="view-content hidden">
                    <div class="card p-6">
                        <!-- Settings Header -->
                        <div class="flex items-center mb-8 pb-6 border-b border-slate-700/50">
                            <div class="flex-shrink-0 mr-6">
                                <div class="w-14 h-14 rounded-lg bg-gradient-to-r from-cyan-500 to-blue-500 flex items-center justify-center text-white">
                                    <i data-lucide="settings" class="h-7 w-7"></i>
                                </div>
                            </div>
                            <div>
                                <h2 class="text-2xl font-bold text-slate-100 mb-1">Settings</h2>
                                <p class="text-slate-400">Customize your Kevko Systems experience</p>
                            </div>
                        </div>

                        <!-- Settings Tab Navigation -->
                        <div class="mb-6 border-b border-slate-700/50">
                            <div class="flex flex-wrap -mb-px">
                                <button id="userSettingsTab" class="settings-tab active inline-flex items-center px-4 py-2 border-b-2 border-cyan-500 text-cyan-400">
                                    <i data-lucide="sliders" class="h-4 w-4 mr-2"></i>
                                    Preferences
                                </button>
                                <button id="appearanceSettingsTab" class="settings-tab inline-flex items-center px-4 py-2 border-b-2 border-transparent text-slate-400 hover:text-slate-300">
                                    <i data-lucide="palette" class="h-4 w-4 mr-2"></i>
                                    Appearance
                                </button>
                                <button id="connectionsSettingsTab" class="settings-tab inline-flex items-center px-4 py-2 border-b-2 border-transparent text-slate-400 hover:text-slate-300">
                                    <i data-lucide="link" class="h-4 w-4 mr-2"></i>
                                    Connections
                                </button>
                                <button id="adminSettingsTab" class="settings-tab admin-only hidden inline-flex items-center px-4 py-2 border-b-2 border-transparent text-slate-400 hover:text-slate-300">
                                    <i data-lucide="shield" class="h-4 w-4 mr-2"></i>
                                    Admin
                                    <span class="ml-2 text-xs bg-red-900/30 text-red-400 px-1.5 py-0.5 rounded-full border border-red-800/50">Admin</span>
                                </button>
                            </div>
                        </div>

                        <!-- Tab Content -->
                        <div class="settings-tab-content">
                            <!-- Preferences Tab (visible by default) -->
                            <div id="userSettingsSection" class="settings-tab-pane">
                                <div class="space-y-6">
                                    <!-- Notification Settings -->
                                    <div class="bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden">
                                        <div class="px-4 py-3 bg-slate-700/30 border-b border-slate-700/50 flex justify-between items-center">
                                            <h3 class="text-md font-medium text-slate-200 flex items-center">
                                                <i data-lucide="bell" class="h-4 w-4 mr-2 text-cyan-400"></i>
                                                Notifications
                                            </h3>
                                            <label class="relative inline-flex items-center cursor-pointer">
                                                <input type="checkbox" id="notificationsEnabledToggle" class="sr-only peer" checked>
                                                <div class="w-11 h-6 bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyan-600"></div>
                                            </label>
                                        </div>
                                        <div class="p-4 space-y-4">
                                            <p class="text-xs text-slate-400 mb-2">Configure how you receive notifications from Kevko Systems.</p>

                                            <!-- Sound Notifications -->
                                            <div class="flex items-center justify-between py-2 border-b border-slate-700/30">
                                                <div>
                                                    <label class="text-slate-300 font-medium text-sm">Sound Notifications</label>
                                                    <p class="text-xs text-slate-400">Play sound for notifications</p>
                                                </div>
                                                <label class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" id="notificationsSoundToggle" class="sr-only peer" checked>
                                                    <div class="w-11 h-6 bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyan-600"></div>
                                                </label>
                                            </div>

                                            <!-- Desktop Notifications -->
                                            <div class="flex items-center justify-between py-2 border-b border-slate-700/30">
                                                <div>
                                                    <label class="text-slate-300 font-medium text-sm">Desktop Notifications</label>
                                                    <p class="text-xs text-slate-400">Show desktop notifications</p>
                                                </div>
                                                <label class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" id="notificationsDesktopToggle" class="sr-only peer" checked>
                                                    <div class="w-11 h-6 bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyan-600"></div>
                                                </label>
                                            </div>

                                            <!-- Update Notifications -->
                                            <div class="flex items-center justify-between py-2 border-b border-slate-700/30">
                                                <div>
                                                    <label class="text-slate-300 font-medium text-sm">Update Notifications</label>
                                                    <p class="text-xs text-slate-400">Notify about system updates</p>
                                                </div>
                                                <label class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" id="notificationsUpdatesToggle" class="sr-only peer" checked>
                                                    <div class="w-11 h-6 bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyan-600"></div>
                                                </label>
                                            </div>

                                            <!-- Service Notifications -->
                                            <div class="flex items-center justify-between py-2">
                                                <div>
                                                    <label class="text-slate-300 font-medium text-sm">Service Notifications</label>
                                                    <p class="text-xs text-slate-400">Notify about service activity</p>
                                                </div>
                                                <label class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" id="notificationsServicesToggle" class="sr-only peer" checked>
                                                    <div class="w-11 h-6 bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyan-600"></div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Privacy Settings -->
                                    <div class="bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden">
                                        <div class="px-4 py-3 bg-slate-700/30 border-b border-slate-700/50 flex justify-between items-center">
                                            <h3 class="text-md font-medium text-slate-200 flex items-center">
                                                <i data-lucide="shield" class="h-4 w-4 mr-2 text-cyan-400"></i>
                                                Privacy
                                            </h3>
                                        </div>
                                        <div class="p-4 space-y-4">
                                            <p class="text-xs text-slate-400 mb-2">Control your privacy settings and what information is shared with others.</p>

                                            <!-- Show Online Status -->
                                            <div class="flex items-center justify-between py-2 border-b border-slate-700/30">
                                                <div>
                                                    <label class="text-slate-300 font-medium text-sm">Show Online Status</label>
                                                    <p class="text-xs text-slate-400">Let others see when you're online</p>
                                                </div>
                                                <label class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" id="privacyOnlineStatusToggle" class="sr-only peer" checked>
                                                    <div class="w-11 h-6 bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyan-600"></div>
                                                </label>
                                            </div>

                                            <!-- Share Activity -->
                                            <div class="flex items-center justify-between py-2 border-b border-slate-700/30">
                                                <div>
                                                    <label class="text-slate-300 font-medium text-sm">Share Activity</label>
                                                    <p class="text-xs text-slate-400">Share your activity with friends</p>
                                                </div>
                                                <label class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" id="privacyShareActivityToggle" class="sr-only peer" checked>
                                                    <div class="w-11 h-6 bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyan-600"></div>
                                                </label>
                                            </div>

                                            <!-- Allow Friend Requests -->
                                            <div class="flex items-center justify-between py-2">
                                                <div>
                                                    <label class="text-slate-300 font-medium text-sm">Allow Friend Requests</label>
                                                    <p class="text-xs text-slate-400">Allow others to send you friend requests</p>
                                                </div>
                                                <label class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" id="privacyFriendRequestsToggle" class="sr-only peer" checked>
                                                    <div class="w-11 h-6 bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyan-600"></div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Service Management -->
                                    <div class="bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden">
                                        <div class="px-4 py-3 bg-slate-700/30 border-b border-slate-700/50 flex justify-between items-center">
                                            <h3 class="text-md font-medium text-slate-200 flex items-center">
                                                <i data-lucide="layout-grid" class="h-4 w-4 mr-2 text-cyan-400"></i>
                                                Service Management
                                            </h3>
                                        </div>
                                        <div class="p-4">
                                            <p class="text-xs text-slate-400 mb-4">Customize your dashboard by adding or removing services.</p>

                                            <div class="bg-slate-700/30 rounded-lg p-4 border border-slate-600/30">
                                                <div class="flex flex-col sm:flex-row items-center justify-between">
                                                    <div class="flex items-center mb-3 sm:mb-0">
                                                        <div class="p-2.5 rounded-lg bg-cyan-500/10 mr-3">
                                                            <i data-lucide="shopping-bag" class="h-5 w-5 text-cyan-500"></i>
                                                        </div>
                                                        <div>
                                                            <h4 class="text-sm font-medium text-slate-200">Service Store</h4>
                                                            <p class="text-xs text-slate-400">Browse and add services to your dashboard</p>
                                                        </div>
                                                    </div>
                                                    <a href="#" class="text-xs bg-cyan-600 hover:bg-cyan-500 text-white px-3 py-1.5 rounded-md flex items-center" data-view="store">
                                                        <i data-lucide="external-link" class="h-3 w-3 mr-1"></i>
                                                        Visit Store
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Appearance Tab (hidden by default) -->
                            <div id="appearanceSettingsSection" class="settings-tab-pane hidden">
                                <div class="space-y-6">
                                    <!-- Theme Settings -->
                                    <div class="bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden">
                                        <div class="px-4 py-3 bg-slate-700/30 border-b border-slate-700/50 flex justify-between items-center">
                                            <h3 class="text-md font-medium text-slate-200 flex items-center">
                                                <i data-lucide="palette" class="h-4 w-4 mr-2 text-cyan-400"></i>
                                                Theme
                                            </h3>
                                        </div>
                                        <div class="p-4 space-y-4">
                                            <p class="text-xs text-slate-400 mb-2">Customize the look and feel of your dashboard.</p>

                                            <!-- Color Scheme -->
                                            <div>
                                                <label class="text-slate-300 font-medium block mb-2 text-sm">Color Scheme</label>
                                                <div class="grid grid-cols-3 gap-3">
                                                    <button class="theme-btn active flex flex-col items-center p-3 bg-slate-700/50 hover:bg-slate-700 border border-cyan-500 rounded-lg">
                                                        <div class="w-full h-10 bg-gradient-to-r from-cyan-500 to-blue-500 rounded-md mb-2"></div>
                                                        <span class="text-xs text-slate-300">Default</span>
                                                    </button>
                                                    <button class="theme-btn flex flex-col items-center p-3 bg-slate-700/50 hover:bg-slate-700 border border-slate-600/50 rounded-lg">
                                                        <div class="w-full h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-md mb-2"></div>
                                                        <span class="text-xs text-slate-300">Vibrant</span>
                                                    </button>
                                                    <button class="theme-btn flex flex-col items-center p-3 bg-slate-700/50 hover:bg-slate-700 border border-slate-600/50 rounded-lg">
                                                        <div class="w-full h-10 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-md mb-2"></div>
                                                        <span class="text-xs text-slate-300">Nature</span>
                                                    </button>
                                                </div>
                                            </div>

                                            <!-- Dark Mode -->
                                            <div class="flex items-center justify-between py-2 border-t border-slate-700/30 mt-3 pt-3">
                                                <div>
                                                    <label class="text-slate-300 font-medium text-sm">Dark Mode</label>
                                                    <p class="text-xs text-slate-400">Always use dark mode</p>
                                                </div>
                                                <label class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" id="darkModeToggle" class="sr-only peer" checked>
                                                    <div class="w-11 h-6 bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyan-600"></div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Text Settings -->
                                    <div class="bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden">
                                        <div class="px-4 py-3 bg-slate-700/30 border-b border-slate-700/50 flex justify-between items-center">
                                            <h3 class="text-md font-medium text-slate-200 flex items-center">
                                                <i data-lucide="type" class="h-4 w-4 mr-2 text-cyan-400"></i>
                                                Text Settings
                                            </h3>
                                        </div>
                                        <div class="p-4 space-y-4">
                                            <!-- Font Size -->
                                            <div>
                                                <label class="text-slate-300 font-medium block mb-2 text-sm">Font Size</label>
                                                <div class="flex items-center space-x-2">
                                                    <button data-font-size="small" class="font-size-btn px-3 py-1.5 bg-slate-700 hover:bg-slate-600 text-slate-300 rounded-md text-sm flex-1 flex justify-center items-center">
                                                        <span class="text-xs mr-2">A</span>Small
                                                    </button>
                                                    <button data-font-size="medium" class="font-size-btn px-3 py-1.5 bg-cyan-600 text-white rounded-md text-sm flex-1 flex justify-center items-center">
                                                        <span class="text-sm mr-2">A</span>Medium
                                                    </button>
                                                    <button data-font-size="large" class="font-size-btn px-3 py-1.5 bg-slate-700 hover:bg-slate-600 text-slate-300 rounded-md text-sm flex-1 flex justify-center items-center">
                                                        <span class="text-base mr-2">A</span>Large
                                                    </button>
                                                </div>
                                                <p class="text-xs text-slate-400 mt-1">Adjust the text size throughout the application.</p>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>

                            <!-- Connections Tab (hidden by default) -->
                            <div id="connectionsSettingsSection" class="settings-tab-pane hidden">
                                <div class="space-y-6">
                                    <!-- Available Connections -->
                                    <div class="bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden">
                                        <div class="px-4 py-3 bg-slate-700/30 border-b border-slate-700/50 flex justify-between items-center">
                                            <h3 class="text-md font-medium text-slate-200 flex items-center">
                                                <i data-lucide="link" class="h-4 w-4 mr-2 text-cyan-400"></i>
                                                Available Connections
                                            </h3>
                                        </div>
                                        <div class="p-4 space-y-4">
                                            <p class="text-xs text-slate-400 mb-2">Connect external services to enhance your Kevko Systems experience.</p>

                                            <!-- Spotify -->
                                            <div class="bg-slate-700/30 rounded-lg p-4 border border-slate-600/30">
                                                <div class="flex flex-col sm:flex-row items-center justify-between">
                                                    <div class="flex items-center mb-3 sm:mb-0">
                                                        <div class="p-2.5 rounded-lg bg-green-500/10 mr-3">
                                                            <i data-lucide="music" class="h-5 w-5 text-green-500"></i>
                                                        </div>
                                                        <div>
                                                            <h4 class="text-sm font-medium text-slate-200">Spotify</h4>
                                                            <p class="text-xs text-slate-400">Connect to control your Spotify playback</p>
                                                        </div>
                                                    </div>
                                                    <a href="/spotify/auth" class="text-xs bg-green-600 hover:bg-green-500 text-white px-3 py-1.5 rounded-md flex items-center">
                                                        <i data-lucide="link" class="h-3 w-3 mr-1"></i>
                                                        Connect
                                                    </a>
                                                </div>
                                            </div>

                                            <!-- Discord -->
                                            <div class="bg-slate-700/30 rounded-lg p-4 border border-slate-600/30">
                                                <div class="flex flex-col sm:flex-row items-center justify-between">
                                                    <div class="flex items-center mb-3 sm:mb-0">
                                                        <div class="p-2.5 rounded-lg bg-indigo-500/10 mr-3">
                                                            <i data-lucide="message-circle" class="h-5 w-5 text-indigo-500"></i>
                                                        </div>
                                                        <div>
                                                            <h4 class="text-sm font-medium text-slate-200">Discord</h4>
                                                            <p class="text-xs text-slate-400">Connect to integrate with Discord</p>
                                                        </div>
                                                    </div>
                                                    <a href="/discord/auth" class="text-xs bg-indigo-600 hover:bg-indigo-500 text-white px-3 py-1.5 rounded-md flex items-center">
                                                        <i data-lucide="link" class="h-3 w-3 mr-1"></i>
                                                        Connect
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Connected Services -->
                                    <div class="bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden">
                                        <div class="px-4 py-3 bg-slate-700/30 border-b border-slate-700/50 flex justify-between items-center">
                                            <h3 class="text-md font-medium text-slate-200 flex items-center">
                                                <i data-lucide="check-circle" class="h-4 w-4 mr-2 text-cyan-400"></i>
                                                Your Connected Services
                                            </h3>
                                        </div>
                                        <div class="p-4">
                                            <!-- Connected services will be loaded here -->
                                            <div id="connectedServicesContainer" class="space-y-3">
                                                <div class="flex justify-center items-center py-6">
                                                    <div class="flex items-center">
                                                        <i data-lucide="loader" class="h-5 w-5 text-slate-500 animate-spin mr-2"></i>
                                                        <span class="text-slate-500 text-sm">Loading connected services...</span>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- No connected services message (hidden by default) -->
                                            <div id="noConnectedServices" class="hidden">
                                                <div class="bg-slate-700/30 p-4 rounded-lg text-center border border-slate-600/30">
                                                    <div class="flex flex-col items-center">
                                                        <div class="p-3 rounded-full bg-slate-600/30 mb-2">
                                                            <i data-lucide="plug-zap-off" class="h-6 w-6 text-slate-400"></i>
                                                        </div>
                                                        <p class="text-sm text-slate-400">You don't have any external services connected yet.</p>
                                                        <a href="#" class="mt-3 text-xs bg-slate-700 hover:bg-slate-600 text-slate-300 px-3 py-1.5 rounded-md flex items-center" data-view="store">
                                                            <i data-lucide="plus" class="h-3 w-3 mr-1"></i>
                                                            Connect a Service
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Connection Permissions -->
                                    <div class="bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden">
                                        <div class="px-4 py-3 bg-slate-700/30 border-b border-slate-700/50 flex justify-between items-center">
                                            <h3 class="text-md font-medium text-slate-200 flex items-center">
                                                <i data-lucide="shield" class="h-4 w-4 mr-2 text-cyan-400"></i>
                                                Connection Permissions
                                            </h3>
                                        </div>
                                        <div class="p-4 space-y-4">
                                            <p class="text-xs text-slate-400 mb-2">Control how connected services interact with your account.</p>

                                            <!-- Auto-connect -->
                                            <div class="flex items-center justify-between py-2 border-b border-slate-700/30">
                                                <div>
                                                    <label class="text-slate-300 font-medium text-sm">Auto-reconnect Services</label>
                                                    <p class="text-xs text-slate-400">Automatically reconnect to services when session expires</p>
                                                </div>
                                                <label class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" id="autoReconnectToggle" class="sr-only peer" checked>
                                                    <div class="w-11 h-6 bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyan-600"></div>
                                                </label>
                                            </div>

                                            <!-- Data Sharing -->
                                            <div class="flex items-center justify-between py-2">
                                                <div>
                                                    <label class="text-slate-300 font-medium text-sm">Share Usage Data</label>
                                                    <p class="text-xs text-slate-400">Allow services to access your usage statistics</p>
                                                </div>
                                                <label class="relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" id="shareDataToggle" class="sr-only peer">
                                                    <div class="w-11 h-6 bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyan-600"></div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Admin Settings Section -->
                        <div id="adminSettingsSection" class="space-y-6 hidden">

                            <!-- Service Updates -->
                            {% include 'includes/admin_updates.html' %}

                            <!-- Active Users -->
                            <div class="bg-slate-800/50 rounded-lg p-4 border border-slate-700/50 mb-6">
                                <div class="flex items-center justify-between mb-3">
                                    <h3 class="text-md font-medium text-slate-200">Active Users</h3>
                                    <div class="flex space-x-2">
                                        <select id="activeTimeFilter" class="text-xs bg-slate-700 border border-slate-600 rounded px-2 py-1 text-slate-300 focus:outline-none focus:ring-1 focus:ring-cyan-500">
                                            <option value="15">Last 15 minutes</option>
                                            <option value="30">Last 30 minutes</option>
                                            <option value="60">Last hour</option>
                                            <option value="360">Last 6 hours</option>
                                            <option value="1440">Last 24 hours</option>
                                        </select>
                                        <button id="refreshActiveUsersBtn" class="text-xs bg-slate-700 hover:bg-slate-600 text-slate-300 px-2 py-1 rounded flex items-center">
                                            <i data-lucide="refresh-cw" class="h-3 w-3 mr-1"></i>
                                            Refresh
                                        </button>
                                    </div>
                                </div>

                                <!-- Active Users Stats -->
                                <div class="grid grid-cols-4 gap-4 mb-4">
                                    <div class="bg-slate-700/30 rounded-lg p-3 border border-slate-600/50 text-center">
                                        <div class="text-xs text-slate-400 mb-1">Chat Users</div>
                                        <div id="chatUserCount" class="text-lg font-medium text-cyan-400">0</div>
                                    </div>
                                    <div class="bg-slate-700/30 rounded-lg p-3 border border-slate-600/50 text-center">
                                        <div class="text-xs text-slate-400 mb-1">Live Users</div>
                                        <div id="liveUserCount" class="text-lg font-medium text-green-400">0</div>
                                    </div>
                                    <div class="bg-slate-700/30 rounded-lg p-3 border border-slate-600/50 text-center">
                                        <div class="text-xs text-slate-400 mb-1">Spotify Users</div>
                                        <div id="spotifyUserCount" class="text-lg font-medium text-purple-400">0</div>
                                    </div>
                                    <div class="bg-slate-700/30 rounded-lg p-3 border border-slate-600/50 text-center">
                                        <div class="text-xs text-slate-400 mb-1">Total Unique</div>
                                        <div id="totalUniqueCount" class="text-lg font-medium text-amber-400">0</div>
                                    </div>
                                </div>

                                <!-- Active Users Tabs -->
                                <div class="border-b border-slate-700 mb-4">
                                    <div class="flex">
                                        <button id="chatUsersTab" class="active-users-tab active relative px-4 py-2 text-slate-200 font-medium focus:outline-none">
                                            <span class="flex items-center">
                                                <i data-lucide="message-square" class="h-3 w-3 mr-1"></i>
                                                Chat
                                            </span>
                                            <span class="absolute bottom-0 left-0 w-full h-0.5 bg-cyan-500 transform scale-x-100 transition-transform origin-left"></span>
                                        </button>
                                        <button id="liveUsersTab" class="active-users-tab relative px-4 py-2 text-slate-400 font-medium focus:outline-none">
                                            <span class="flex items-center">
                                                <i data-lucide="users" class="h-3 w-3 mr-1"></i>
                                                Live
                                            </span>
                                            <span class="absolute bottom-0 left-0 w-full h-0.5 bg-cyan-500 transform scale-x-0 transition-transform origin-left"></span>
                                        </button>
                                        <button id="spotifyUsersTab" class="active-users-tab relative px-4 py-2 text-slate-400 font-medium focus:outline-none">
                                            <span class="flex items-center">
                                                <i data-lucide="music" class="h-3 w-3 mr-1"></i>
                                                Spotify
                                            </span>
                                            <span class="absolute bottom-0 left-0 w-full h-0.5 bg-cyan-500 transform scale-x-0 transition-transform origin-left"></span>
                                        </button>
                                    </div>
                                </div>

                                <!-- Active Users Lists -->
                                <div id="activeUsersLists" class="relative">
                                    <div id="chatUsersList" class="active-users-list space-y-2">
                                        <!-- Chat users will be loaded dynamically -->
                                        <div class="text-center text-slate-400 text-sm py-2">Loading users...</div>
                                    </div>
                                    <div id="liveUsersList" class="active-users-list hidden space-y-2">
                                        <!-- Live users will be loaded dynamically -->
                                    </div>
                                    <div id="spotifyUsersList" class="active-users-list hidden space-y-2">
                                        <!-- Spotify users will be loaded dynamically -->
                                    </div>
                                </div>
                            </div>

                            <!-- Admin Management -->
                            <div class="bg-slate-800/50 rounded-lg p-4 border border-slate-700/50">
                                <div class="flex items-center justify-between mb-3">
                                    <h3 class="text-md font-medium text-slate-200">Admin Management</h3>
                                    <div class="flex space-x-2">
                                        <button id="addAdminBtn" class="text-xs bg-slate-700 hover:bg-slate-600 text-slate-300 px-2 py-1 rounded flex items-center">
                                            <i data-lucide="user-plus" class="h-3 w-3 mr-1"></i>
                                            Add Admin
                                        </button>
                                        <button id="refreshAdminsBtn" class="text-xs bg-slate-700 hover:bg-slate-600 text-slate-300 px-2 py-1 rounded flex items-center">
                                            <i data-lucide="refresh-cw" class="h-3 w-3 mr-1"></i>
                                            Refresh
                                        </button>
                                    </div>
                                </div>

                                <!-- Admin List -->
                                <div id="adminList" class="space-y-2">
                                    <!-- Admins will be loaded dynamically -->
                                </div>
                            </div>

                            <!-- Credit Management Section -->
                            {% include 'includes/admin_credits.html' %}



                            <!-- Service Restrictions -->
                            <link rel="stylesheet" href="/static/css/service-restrictions.css">
                            <div class="service-restrictions-container">
                                <div class="service-restrictions-header">
                                    <h3 class="service-restrictions-title">
                                        <i data-lucide="shield-alert" class="h-5 w-5"></i>
                                        Service Restrictions
                                    </h3>
                                    <div class="service-restrictions-actions">
                                        <button class="btn-restriction btn-primary">
                                            <i data-lucide="plus" class="h-3.5 w-3.5"></i>
                                            New Restriction
                                        </button>
                                        <button class="btn-restriction btn-secondary">
                                            <i data-lucide="filter" class="h-3.5 w-3.5"></i>
                                            Filter
                                        </button>
                                    </div>
                                </div>

                                <!-- Service Selector -->
                                <div class="service-selector">
                                    <label class="service-selector-label">Select service to configure restrictions</label>
                                    <select class="service-select">
                                        <option value="all">All Services</option>
                                        <option value="kevkoFy">KevkoFy</option>
                                        <option value="kevkoHome">KevkoHome</option>
                                        <option value="kevkoCloud">KevkoCloud</option>
                                        <option value="kevkoWeather">KevkoWeather</option>
                                        <option value="kevkoNotes">KevkoNotes</option>
                                    </select>
                                </div>

                                <!-- Restriction Types -->
                                <div class="restriction-cards">

                                    <!-- Access Control -->
                                    <div class="restriction-card access-control">
                                        <div class="restriction-card-header">
                                            <div class="restriction-card-title">
                                                <i data-lucide="shield-off" class="text-red-400"></i>
                                                Access Control
                                            </div>
                                            <div class="restriction-card-actions">
                                                <button id="viewAllAccessControlButton" class="btn-card tooltip" data-tooltip="View all restrictions">View All</button>
                                            </div>
                                        </div>
                                        <div class="restriction-card-description">Restrict users from accessing specific services</div>
                                        <div class="restriction-card-content" id="accessControlContainer">
                                            <!-- Access control restrictions will be loaded dynamically -->
                                            <div class="flex justify-center items-center py-4">
                                                <div class="flex items-center">
                                                    <i data-lucide="loader" class="h-4 w-4 text-slate-400 animate-spin mr-2"></i>
                                                    <span class="text-slate-400 text-sm">Loading restrictions...</span>
                                                </div>
                                            </div>
                                            <button id="addAccessControlButton" class="btn-add-item">
                                                <i data-lucide="plus" class="h-3.5 w-3.5"></i>
                                                Add Service Restriction
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Storage Limits -->
                                    <div class="restriction-card storage-limits">
                                        <div class="restriction-card-header">
                                            <div class="restriction-card-title">
                                                <i data-lucide="hard-drive" class="text-green-400"></i>
                                                Storage Limits
                                            </div>
                                            <div class="restriction-card-actions">
                                                <button class="btn-card tooltip" data-tooltip="Edit limits">Edit</button>
                                                <button class="btn-card btn-delete tooltip" data-tooltip="Remove limits">×</button>
                                            </div>
                                        </div>
                                        <div class="restriction-card-description">Set storage limits for users</div>
                                        <div class="restriction-card-content">
                                            <div class="user-list">
                                                <div class="user-item">
                                                    <span class="user-name">@kevko</span>
                                                    <span class="user-limit">5 GB / 10 GB</span>
                                                </div>
                                                <div class="storage-progress">
                                                    <div class="storage-progress-bar" style="width: 50%"></div>
                                                </div>
                                                <div class="user-item">
                                                    <span class="user-name">@johnsmith</span>
                                                    <span class="user-limit">2 GB / 5 GB</span>
                                                </div>
                                                <div class="storage-progress">
                                                    <div class="storage-progress-bar" style="width: 40%"></div>
                                                </div>
                                            </div>
                                            <button class="btn-add-item">
                                                <i data-lucide="plus" class="h-3.5 w-3.5"></i>
                                                Add User Limit
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Time Restrictions -->
                                    <div class="restriction-card time-restrictions">
                                        <div class="restriction-card-header">
                                            <div class="restriction-card-title">
                                                <i data-lucide="clock" class="text-amber-400"></i>
                                                Time Restrictions
                                            </div>
                                            <div class="restriction-card-actions">
                                                <button class="btn-card tooltip" data-tooltip="Edit schedule">Edit</button>
                                                <button class="btn-card btn-delete tooltip" data-tooltip="Remove schedule">×</button>
                                            </div>
                                        </div>
                                        <div class="restriction-card-description">Limit service usage to specific times</div>
                                        <div class="restriction-card-content">
                                            <div class="user-list">
                                                <div class="user-item">
                                                    <span class="user-name">Weekdays</span>
                                                    <span class="user-limit">9:00 AM - 5:00 PM</span>
                                                </div>
                                                <div class="user-item">
                                                    <span class="user-name">Weekends</span>
                                                    <span class="user-limit">No restrictions</span>
                                                </div>
                                            </div>
                                            <button class="btn-add-item">
                                                <i data-lucide="plus" class="h-3.5 w-3.5"></i>
                                                Add Time Rule
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Feature Restrictions -->
                                    <div class="restriction-card feature-restrictions">
                                        <div class="restriction-card-header">
                                            <div class="restriction-card-title">
                                                <i data-lucide="sliders" class="text-purple-400"></i>
                                                Feature Restrictions
                                            </div>
                                            <div class="restriction-card-actions">
                                                <button id="viewAllFeatureRestrictions" class="btn-card tooltip" data-tooltip="View all restrictions">View All</button>
                                            </div>
                                        </div>
                                        <div class="restriction-card-description">Limit user threads and conversation sets</div>
                                        <div class="restriction-card-content" id="featureRestrictionsContainer">
                                            <!-- Feature restrictions will be loaded dynamically -->
                                            <div class="user-item">
                                                <span class="user-name">@gitter</span>
                                                <span class="user-limit">Threads: 6, Conv. Sets: 10</span>
                                            </div>
                                            <div class="user-item">
                                                <span class="user-name">@gitland</span>
                                                <span class="user-limit">Threads: 6, Conv. Sets: 10</span>
                                            </div>
                                            <button id="addFeatureRestriction" class="btn-add-item">
                                                <i data-lucide="plus" class="h-3.5 w-3.5"></i>
                                                Add Restriction
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Logs Section -->
                                <div class="logs-section">
                                    <div class="logs-header">
                                        <h3 class="logs-title">
                                            <i data-lucide="file-text" class="h-4 w-4 text-cyan-400"></i>
                                            System Logs
                                        </h3>
                                        <button id="openLogsBtn" class="btn-restriction btn-secondary">
                                            <i data-lucide="list" class="h-3.5 w-3.5"></i>
                                            View Logs
                                        </button>
                                    </div>
                                    <div class="logs-content">
                                        <p class="logs-description">Monitor API requests and user activity across all services.</p>

                                        <!-- Recent Logs Preview -->
                                        <div class="logs-preview" id="logsPreview">
                                            <div class="log-entry">
                                                <span class="log-time">10:45:23</span>
                                                <span class="log-type">INFO</span>
                                                <span class="log-message">User @kevko accessed KevkoFy service</span>
                                            </div>
                                            <div class="log-entry">
                                                <span class="log-time">10:42:15</span>
                                                <span class="log-type warning">WARNING</span>
                                                <span class="log-message">Storage limit approaching for @johnsmith</span>
                                            </div>
                                            <div class="log-entry">
                                                <span class="log-time">10:40:07</span>
                                                <span class="log-type">INFO</span>
                                                <span class="log-message">Feature restriction applied to @gitland</span>
                                            </div>
                                            <div class="log-entry">
                                                <span class="log-time">10:35:52</span>
                                                <span class="log-type error">ERROR</span>
                                                <span class="log-message">Access denied for @visitor to KevkoCloud</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Add New Restriction Type -->
                                <button class="btn-add-restriction">
                                    <i data-lucide="plus" class="h-4 w-4"></i>
                                    Add New Restriction Type
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Friends View -->
                <div id="friendsView" class="view-content hidden">
                    <div class="card p-6">
                        <!-- Friends Header -->
                        <div class="flex items-center mb-8 pb-6 border-b border-slate-700/50">
                            <div class="flex-shrink-0 mr-6">
                                <div class="w-14 h-14 rounded-lg bg-gradient-to-r from-cyan-500 to-blue-500 flex items-center justify-center text-white shadow-lg shadow-cyan-500/20">
                                    <i data-lucide="message-circle" class="h-7 w-7"></i>
                                </div>
                            </div>
                            <div>
                                <h2 class="text-2xl font-bold text-slate-100 mb-1">Friends</h2>
                                <p class="text-slate-400">Chat with your friends and invite them to live sessions</p>
                            </div>
                        </div>

                        <!-- Friends Content -->
                        <div class="flex flex-col md:flex-row gap-4">
                            <!-- Chat Area (Left/Center) - Takes 70% of the space on larger screens -->
                            <div class="w-full md:w-[70%] order-2 md:order-1">
                                <div class="bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden h-full flex flex-col shadow-lg shadow-black/10">
                                    <!-- Chat Header -->
                                    <div class="px-3 sm:px-4 py-2 sm:py-3 bg-slate-700/30 border-b border-slate-700/50 flex justify-between items-center friend-header">
                                        <div class="flex items-center">
                                            <div id="activeFriendAvatar" class="relative mr-3">
                                                <!-- Friend avatar will be inserted here -->
                                            </div>
                                            <div>
                                                <h3 id="activeFriendName" class="text-sm sm:text-md font-medium text-slate-200">Select a friend</h3>
                                                <div class="flex items-center space-x-2">
                                                    <div id="activeFriendStatus" class="text-xs text-slate-400">Offline</div>
                                                    <div id="encryptionStatus" class="encryption-badge hidden">
                                                        <i data-lucide="lock" class="h-3 w-3"></i>
                                                        <span class="text-xs">End-to-End Encrypted</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex space-x-1 sm:space-x-2">
                                            <button id="inviteToLiveBtn" class="friend-action-btn text-xs bg-cyan-600 hover:bg-cyan-500 text-white px-1 sm:px-2 py-1 rounded-md flex items-center shadow-md shadow-cyan-500/10 transition-all hidden">
                                                <i data-lucide="video" class="h-3 w-3 mr-1"></i>
                                                <span class="hidden sm:inline">Invite to Live</span>
                                                <span class="sm:hidden">Live</span>
                                            </button>
                                            <button id="removeFriendBtn" class="friend-action-btn text-xs bg-slate-700 hover:bg-red-900/70 text-slate-300 hover:text-red-300 px-1 sm:px-2 py-1 rounded-md flex items-center shadow-md shadow-black/10 transition-all hidden">
                                                <i data-lucide="user-minus" class="h-3 w-3 mr-1"></i>
                                                <span class="hidden sm:inline">Remove</span>
                                                <span class="sm:hidden">X</span>
                                            </button>
                                            <button id="groupInfoBtn" class="friend-action-btn text-xs bg-slate-700 hover:bg-slate-600 text-slate-300 hover:text-slate-100 px-1 sm:px-2 py-1 rounded-md flex items-center shadow-md shadow-black/10 transition-all hidden">
                                                <i data-lucide="menu" class="h-3 w-3 mr-1"></i>
                                                <span class="hidden sm:inline">Menu</span>
                                                <span class="sm:hidden">Menu</span>
                                            </button>
                                            <!-- Hidden buttons that will be moved to the modal -->
                                            <button id="exitGroupChatBtn" class="hidden"></button>
                                            <button id="deleteGroupChatBtn" class="hidden"></button>
                                        </div>
                                    </div>

                                    <!-- Chat Messages Area -->
                                    <div class="flex-grow overflow-hidden" style="height: calc(100vh - 300px); max-height: 500px;">
                                        <!-- Welcome message shown when no chat is selected -->
                                        <div id="welcomeMessage" class="flex flex-col items-center justify-center h-full p-4 sm:p-6 text-center">
                                            <div class="w-16 h-16 sm:w-20 sm:h-20 rounded-full welcome-icon flex items-center justify-center mb-4 sm:mb-5 shadow-lg">
                                                <i data-lucide="message-circle" class="h-8 w-8 sm:h-10 sm:w-10 text-white"></i>
                                            </div>
                                            <h3 class="text-lg sm:text-xl font-medium text-slate-200 mb-2 sm:mb-3">Welcome to Friends</h3>
                                            <p class="text-xs sm:text-sm text-slate-400 max-w-md">Select a friend from the list to start chatting or add new friends using the Add button.</p>
                                        </div>

                                        <!-- Messages container for chat messages -->
                                        <div id="messagesContainer" class="overflow-y-auto p-3 sm:p-4 hidden">
                                            <!-- Messages will be dynamically loaded here -->
                                        </div>
                                    </div>

                                    <!-- Chat Input Area -->
                                    <div class="p-3 sm:p-4 border-t border-slate-700/50">
                                        <!-- Image Preview Container -->
                                        <div id="imagePreviewContainer" class="hidden flex flex-wrap gap-2 mb-2 p-2 bg-slate-700/30 rounded-lg border border-slate-600/50 relative">
                                            <!-- Image previews will be added here dynamically -->
                                        </div>

                                        <div class="message-input-container">
                                            <!-- Image Upload Button -->
                                            <button id="imageBtn">
                                                <i data-lucide="plus" class="h-5 w-5"></i>
                                            </button>
                                            <!-- Hidden File Input -->
                                            <input type="file" id="imageFileInput" class="hidden" accept="image/*" multiple>

                                            <textarea id="messageInput" rows="1" placeholder="Type a message..." class="w-full bg-slate-700/70 border border-slate-600/70 rounded-lg px-3 py-2 pl-10 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500/50 resize-none shadow-inner shadow-black/10" disabled></textarea>
                                            <button id="sendButton" class="absolute right-2 sm:right-3 bottom-2 sm:bottom-3 bg-cyan-600 hover:bg-cyan-500 text-white rounded-md shadow-md shadow-cyan-500/10 transition-all disabled:opacity-50 disabled:cursor-not-allowed">
                                                <i data-lucide="send" class="h-4 w-4"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Sidebar - Friend List (30% of the space) -->
                            <div class="w-full md:w-[30%] order-1 md:order-2">
                                <div class="bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden shadow-lg shadow-black/10">
                                    <div class="px-3 sm:px-4 py-2 sm:py-3 bg-slate-700/30 border-b border-slate-700/50 flex justify-between items-center friend-header">
                                        <div class="flex items-center">
                                            <h3 class="text-sm sm:text-md font-medium text-slate-200 flex items-center">
                                                <i data-lucide="users" class="h-4 w-4 mr-2 text-cyan-400"></i>
                                                Friends
                                            </h3>
                                            <button id="toggleFriendListBtn" class="panel-toggle-btn ml-1 sm:ml-2 p-1 rounded-full text-slate-400 hover:text-slate-200">
                                                <i data-lucide="chevron-up" class="h-3 sm:h-4 w-3 sm:w-4"></i>
                                            </button>
                                        </div>
                                        <div class="flex items-center">
                                            <button id="friendsMenuBtn" class="p-1.5 rounded-full bg-slate-700 hover:bg-slate-600 text-slate-300 hover:text-white transition-all relative">
                                                <i data-lucide="more-horizontal" class="h-4 w-4"></i>
                                                <span id="menuRequestBadge" class="absolute -top-1 -right-1 bg-cyan-600 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center hidden">0</span>
                                            </button>
                                        </div>
                                    </div>
                                    <div id="friendListPanel" class="collapsible-panel p-2 sm:p-3">
                                        <!-- Friend Search -->
                                        <div class="mb-2 sm:mb-3">
                                            <div class="relative">
                                                <input type="text" id="friendSearchInput" placeholder="Search friends..." class="w-full bg-slate-700/70 border border-slate-600/70 rounded-md px-2 sm:px-3 py-1.5 sm:py-2 text-slate-200 text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500/50 pl-7 sm:pl-9 transition-all">
                                                <i data-lucide="search" class="h-3 sm:h-4 w-3 sm:w-4 text-slate-400 absolute left-2 sm:left-3 top-2 sm:top-2.5"></i>
                                                <button id="toggleSearchBtn" class="absolute right-2 sm:right-3 top-2 sm:top-2.5 text-slate-400 hover:text-slate-200">
                                                    <i data-lucide="x" class="h-3 sm:h-4 w-3 sm:w-4"></i>
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Friend List -->
                                        <div id="friendList" class="space-y-1 sm:space-y-2 max-h-[300px] sm:max-h-[400px] overflow-y-auto pr-1">
                                            <!-- Friends will be dynamically loaded here -->
                                            <div class="flex justify-center items-center py-4 sm:py-6">
                                                <div class="flex items-center">
                                                    <i data-lucide="loader" class="h-4 sm:h-5 w-4 sm:w-5 text-slate-500 animate-spin mr-2"></i>
                                                    <span class="text-slate-500 text-xs sm:text-sm">Loading friends...</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Profile View -->
                <div id="profileView" class="view-content hidden">
                    <div class="card p-6">
                        <!-- Profile Header with Avatar -->
                        <div class="flex flex-col md:flex-row items-center md:items-start mb-8 pb-6 border-b border-slate-700/50">
                            <div class="relative mb-4 md:mb-0 md:mr-6">
                                <div id="profileAvatar" class="w-24 h-24 rounded-full bg-gradient-to-r from-cyan-500 to-blue-500 flex items-center justify-center text-white text-3xl font-bold overflow-hidden">
                                    <!-- Avatar will be populated by JS -->
                                    <span id="profileInitials">KS</span>
                                </div>
                                <button id="changeAvatarBtn" class="absolute bottom-0 right-0 bg-slate-800 hover:bg-slate-700 text-cyan-400 rounded-full p-1.5 border border-slate-600" title="Change profile picture">
                                    <i data-lucide="camera" class="h-4 w-4"></i>
                                </button>
                            </div>
                            <div class="text-center md:text-left flex-grow">
                                <h2 class="text-2xl font-bold text-slate-100 mb-1" id="profileDisplayName">Loading...</h2>
                                <p id="profileEmail" class="text-slate-400 mb-3"><EMAIL></p>
                                <div class="flex flex-wrap justify-center md:justify-start gap-2">
                                    <span class="bg-cyan-900/30 text-cyan-400 text-xs px-2.5 py-1 rounded-full border border-cyan-800/50 flex items-center">
                                        <i data-lucide="calendar" class="h-3 w-3 mr-1"></i>
                                        <span id="profileJoinDate">Member since: Loading...</span>
                                    </span>
                                    <span id="accountTypeTag" class="bg-slate-700/50 text-slate-300 text-xs px-2.5 py-1 rounded-full border border-slate-600/50 flex items-center">
                                        <i data-lucide="user" class="h-3 w-3 mr-1"></i>
                                        <span>Standard Account</span>
                                    </span>
                                </div>
                            </div>
                        </div>

                        <!-- Profile Tabs Navigation -->
                        <div class="mb-6 border-b border-slate-700/50">
                            <div class="flex flex-wrap -mb-px overflow-x-auto pb-1">
                                <button id="profileTabAccount" class="profile-tab active inline-flex items-center px-4 py-2 border-b-2 border-cyan-500 text-cyan-400">
                                    <i data-lucide="user" class="h-4 w-4 mr-2"></i>
                                    Account
                                </button>
                                <button id="profileTabUsage" class="profile-tab inline-flex items-center px-4 py-2 border-b-2 border-transparent text-slate-400 hover:text-slate-300">
                                    <i data-lucide="bar-chart-2" class="h-4 w-4 mr-2"></i>
                                    Usage
                                </button>
                                <button id="profileTabSecurity" class="profile-tab inline-flex items-center px-4 py-2 border-b-2 border-transparent text-slate-400 hover:text-slate-300">
                                    <i data-lucide="shield" class="h-4 w-4 mr-2"></i>
                                    Security
                                </button>
                                <button id="profileTabData" class="profile-tab inline-flex items-center px-4 py-2 border-b-2 border-transparent text-slate-400 hover:text-slate-300">
                                    <i data-lucide="database" class="h-4 w-4 mr-2"></i>
                                    Data
                                </button>
                            </div>
                        </div>

                        <!-- Tab Content -->
                        <div class="profile-tab-content">
                            <!-- Account Tab (visible by default) -->
                            <div id="profileContentAccount" class="profile-tab-pane">
                                <div class="space-y-6">
                                    <!-- User Information Card -->
                                    <div class="profile-section bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden">
                                        <div class="profile-section-header px-4 py-3 bg-slate-700/30 border-b border-slate-700/50 flex justify-between items-center">
                                            <h3 class="text-md font-medium text-slate-200 flex items-center">
                                                <i data-lucide="user" class="h-4 w-4 mr-2 text-cyan-400"></i>
                                                User Information
                                            </h3>
                                        </div>
                                        <div class="profile-section-content p-4 space-y-4">
                                            <!-- Username -->
                                            <div class="profile-input-group">
                                                <label class="text-slate-300 font-medium block mb-1.5 text-sm">Username</label>
                                                <div class="flex items-center">
                                                    <div class="relative flex-grow max-w-md">
                                                        <input type="text" id="profileUsername" class="profile-input w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500 pr-10" value="">
                                                        <button id="updateUsernameBtn" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-cyan-400 hover:text-cyan-300 p-1 rounded-full hover:bg-slate-600/50" title="Save username">
                                                            <i data-lucide="save" class="h-4 w-4"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                <p class="text-xs text-slate-400 mt-1">This is how you'll appear across Kevko Systems.</p>
                                            </div>


                                        </div>
                                    </div>

                                    <!-- Preferences Card -->
                                    <div class="profile-section bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden">
                                        <div class="profile-section-header px-4 py-3 bg-slate-700/30 border-b border-slate-700/50 flex justify-between items-center">
                                            <h3 class="text-md font-medium text-slate-200 flex items-center">
                                                <i data-lucide="settings" class="h-4 w-4 mr-2 text-cyan-400"></i>
                                                Account Preferences
                                            </h3>
                                        </div>
                                        <div class="profile-section-content p-4 space-y-4">
                                            <!-- Email Notifications -->
                                            <div class="flex items-center justify-between">
                                                <div>
                                                    <label class="text-slate-300 font-medium block text-sm">Email Notifications</label>
                                                    <p class="text-xs text-slate-400">Receive updates and notifications via email</p>
                                                </div>
                                                <label class="profile-toggle relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" id="emailNotificationsToggle" class="sr-only peer" checked>
                                                    <div class="profile-toggle-slider w-11 h-6 bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyan-600"></div>
                                                </label>
                                            </div>

                                            <!-- Theme Preference -->
                                            <div class="flex items-center justify-between">
                                                <div>
                                                    <label class="text-slate-300 font-medium block text-sm">Dark Mode</label>
                                                    <p class="text-xs text-slate-400">Always use dark mode for the interface</p>
                                                </div>
                                                <label class="profile-toggle relative inline-flex items-center cursor-pointer">
                                                    <input type="checkbox" id="darkModeToggle" class="sr-only peer" checked>
                                                    <div class="profile-toggle-slider w-11 h-6 bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyan-600"></div>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Usage Tab (hidden by default) -->
                            <div id="profileContentUsage" class="profile-tab-pane hidden">
                                <div class="space-y-6">
                                    <!-- Credits Usage Card -->
                                    <div class="bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden">
                                        <div class="px-4 py-3 bg-gradient-to-r from-amber-900/30 to-yellow-900/30 border-b border-slate-700/50">
                                            <h3 class="text-md font-medium text-slate-200 flex items-center">
                                                <i data-lucide="coins" class="h-4 w-4 mr-2 text-amber-400"></i>
                                                AI Credits
                                            </h3>
                                        </div>
                                        <div id="userCreditsContainer">
                                            <div class="flex justify-center items-center py-4">
                                                <div class="flex items-center">
                                                    <i data-lucide="loader" class="h-4 w-4 text-slate-400 animate-spin mr-2"></i>
                                                    <span class="text-slate-400 text-sm">Loading credits...</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Chat Usage Card -->
                                    <div class="bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden">
                                        <div class="px-4 py-3 bg-gradient-to-r from-blue-900/30 to-cyan-900/30 border-b border-slate-700/50">
                                            <h3 class="text-md font-medium text-slate-200 flex items-center">
                                                <i data-lucide="message-square" class="h-4 w-4 mr-2 text-blue-400"></i>
                                                Chat Usage
                                            </h3>
                                        </div>
                                        <div class="p-4">
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <div class="bg-slate-700/30 rounded-lg p-4 border border-slate-600/30">
                                                    <div class="flex items-center justify-between mb-2">
                                                        <span class="text-sm text-slate-400">Threads</span>
                                                        <span class="text-xs bg-blue-900/40 text-blue-300 px-2 py-0.5 rounded-full border border-blue-800/50">
                                                            <span id="chatThreadCount">0</span> / 10
                                                        </span>
                                                    </div>
                                                    <div class="text-2xl font-bold text-slate-200 mb-1" id="chatThreadCountValue">0</div>
                                                    <div class="w-full bg-slate-800/50 rounded-full h-1.5">
                                                        <div id="chatThreadCountProgress" class="bg-gradient-to-r from-blue-500 to-cyan-500 h-1.5 rounded-full" style="width: 0%"></div>
                                                    </div>
                                                </div>
                                                <div class="bg-slate-700/30 rounded-lg p-4 border border-slate-600/30">
                                                    <div class="flex items-center justify-between mb-2">
                                                        <span class="text-sm text-slate-400">Conversation Sets</span>
                                                        <span class="text-xs bg-cyan-900/40 text-cyan-300 px-2 py-0.5 rounded-full border border-cyan-800/50">
                                                            <span id="chatConversationSetsCount">0</span> / <span id="chatMaxConversationSets">15</span>
                                                        </span>
                                                    </div>
                                                    <div class="text-2xl font-bold text-slate-200 mb-1" id="chatConversationSetsValue">0</div>
                                                    <div class="w-full bg-slate-800/50 rounded-full h-1.5">
                                                        <div id="chatConversationSetsProgress" class="bg-gradient-to-r from-cyan-500 to-teal-500 h-1.5 rounded-full" style="width: 0%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Live Usage Card -->
                                    <div class="bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden">
                                        <div class="px-4 py-3 bg-gradient-to-r from-purple-900/30 to-pink-900/30 border-b border-slate-700/50">
                                            <h3 class="text-md font-medium text-slate-200 flex items-center">
                                                <i data-lucide="users" class="h-4 w-4 mr-2 text-purple-400"></i>
                                                Live Usage
                                            </h3>
                                        </div>
                                        <div class="p-4">
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                                <div class="bg-slate-700/30 rounded-lg p-4 border border-slate-600/30">
                                                    <div class="flex items-center justify-between mb-2">
                                                        <span class="text-sm text-slate-400">Rooms</span>
                                                        <span class="text-xs bg-purple-900/40 text-purple-300 px-2 py-0.5 rounded-full border border-purple-800/50">
                                                            <span id="liveRoomCount">0</span> / 5
                                                        </span>
                                                    </div>
                                                    <div class="text-2xl font-bold text-slate-200 mb-1" id="liveRoomCountValue">0</div>
                                                    <div class="w-full bg-slate-800/50 rounded-full h-1.5">
                                                        <div id="liveRoomCountProgress" class="bg-gradient-to-r from-purple-500 to-pink-500 h-1.5 rounded-full" style="width: 0%"></div>
                                                    </div>
                                                </div>
                                                <div class="bg-slate-700/30 rounded-lg p-4 border border-slate-600/30">
                                                    <div class="flex items-center justify-between mb-2">
                                                        <span class="text-sm text-slate-400">Conversation Sets</span>
                                                        <span class="text-xs bg-pink-900/40 text-pink-300 px-2 py-0.5 rounded-full border border-pink-800/50">
                                                            <span id="liveConversationSetsCount">0</span> / <span id="liveMaxConversationSets">20</span>
                                                        </span>
                                                    </div>
                                                    <div class="text-2xl font-bold text-slate-200 mb-1" id="liveConversationSetsValue">0</div>
                                                    <div class="w-full bg-slate-800/50 rounded-full h-1.5">
                                                        <div id="liveConversationSetsProgress" class="bg-gradient-to-r from-pink-500 to-rose-500 h-1.5 rounded-full" style="width: 0%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Friends Usage Card -->
                                    <div class="bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden">
                                        <div class="px-4 py-3 bg-gradient-to-r from-green-900/30 to-emerald-900/30 border-b border-slate-700/50">
                                            <h3 class="text-md font-medium text-slate-200 flex items-center">
                                                <i data-lucide="heart" class="h-4 w-4 mr-2 text-green-400"></i>
                                                Friends
                                            </h3>
                                        </div>
                                        <div class="p-4">
                                            <div class="bg-slate-700/30 rounded-lg p-4 border border-slate-600/30">
                                                <div class="flex items-center justify-between mb-2">
                                                    <span class="text-sm text-slate-400">Friend Count</span>
                                                    <span class="text-xs bg-green-900/40 text-green-300 px-2 py-0.5 rounded-full border border-green-800/50">
                                                        <span id="friendCount">0</span> / 7
                                                    </span>
                                                </div>
                                                <div class="text-2xl font-bold text-slate-200 mb-1" id="friendCountValue">0</div>
                                                <div class="w-full bg-slate-800/50 rounded-full h-1.5">
                                                    <div id="friendCountProgress" class="bg-gradient-to-r from-green-500 to-emerald-500 h-1.5 rounded-full" style="width: 0%"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Security Tab (hidden by default) -->
                            <div id="profileContentSecurity" class="profile-tab-pane hidden">
                                <div class="space-y-6">
                                    <!-- Password Card -->
                                    <div id="passwordSection" class="profile-section bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden">
                                        <div class="profile-section-header px-4 py-3 bg-slate-700/30 border-b border-slate-700/50 flex justify-between items-center">
                                            <h3 class="text-md font-medium text-slate-200 flex items-center">
                                                <i data-lucide="key" class="h-4 w-4 mr-2 text-cyan-400"></i>
                                                Password
                                            </h3>
                                        </div>
                                        <div class="profile-section-content p-4">
                                            <p class="text-sm text-slate-300 mb-3">Secure your account with a strong password.</p>
                                            <button id="resetPasswordBtn" class="bg-slate-700 hover:bg-slate-600 text-slate-200 rounded-md px-4 py-2 flex items-center justify-center transition-colors duration-200">
                                                <i data-lucide="key" class="h-4 w-4 mr-2"></i>
                                                Change Password
                                            </button>
                                            <p class="text-xs text-slate-400 mt-2">Last changed: Never</p>
                                        </div>
                                    </div>


                                    <!-- Google Account Notice (shown only for Google users) -->
                                    <div id="googleAccountSection" class="profile-section hidden bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden">
                                        <div class="profile-section-header px-4 py-3 bg-slate-700/30 border-b border-slate-700/50 flex justify-between items-center">
                                            <h3 class="text-md font-medium text-slate-200 flex items-center">
                                                <i data-lucide="google" class="h-4 w-4 mr-2 text-cyan-400"></i>
                                                Google Account
                                            </h3>
                                        </div>
                                        <div class="profile-section-content p-4">
                                            <div class="bg-slate-700/50 text-slate-300 rounded-md px-4 py-3 flex items-center">
                                                <i data-lucide="info" class="h-5 w-5 mr-3 text-amber-400 flex-shrink-0"></i>
                                                <span>You're signed in with Google. Password management and security settings are handled through your Google account.</span>
                                            </div>
                                            <a href="https://myaccount.google.com/security" target="_blank" class="mt-3 inline-flex items-center text-sm text-cyan-400 hover:text-cyan-300 transition-colors duration-200">
                                                <i data-lucide="external-link" class="h-3.5 w-3.5 mr-1"></i>
                                                Manage Google Account Security
                                            </a>
                                        </div>
                                    </div>

                                    <!-- Two-Factor Authentication -->
                                    <div id="twoFactorSection" class="profile-section bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden">
                                        <div class="profile-section-header px-4 py-3 bg-slate-700/30 border-b border-slate-700/50 flex justify-between items-center">
                                            <h3 class="text-md font-medium text-slate-200 flex items-center">
                                                <i data-lucide="shield-check" class="h-4 w-4 mr-2 text-cyan-400"></i>
                                                Two-Factor Authentication
                                            </h3>
                                            <label class="profile-toggle relative inline-flex items-center cursor-pointer">
                                                <input type="checkbox" id="twoFactorToggle" class="sr-only peer">
                                                <div class="profile-toggle-slider w-11 h-6 bg-slate-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-slate-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-cyan-600"></div>
                                            </label>
                                        </div>
                                        <div class="p-4">
                                            <p class="text-sm text-slate-300 mb-2">Add an extra layer of security to your account.</p>
                                            <p class="text-xs text-slate-400">When enabled, you'll receive a verification code by email each time you try to log in.</p>
                                        </div>
                                    </div>

                                    <!-- Login History -->
                                    <div class="bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden">
                                        <div class="px-4 py-3 bg-slate-700/30 border-b border-slate-700/50 flex justify-between items-center">
                                            <h3 class="text-md font-medium text-slate-200 flex items-center">
                                                <i data-lucide="history" class="h-4 w-4 mr-2 text-cyan-400"></i>
                                                Login History
                                            </h3>
                                        </div>
                                        <div class="p-4">
                                            <!-- Login history list will be populated by JS -->
                                            <div class="login-history-list">
                                                <div class="bg-slate-700/30 rounded-lg p-3 border border-slate-600/50 mb-2">
                                                    <div class="flex justify-between items-center mb-1">
                                                        <div class="flex items-center">
                                                            <i data-lucide="monitor" class="h-4 w-4 text-cyan-400 mr-2"></i>
                                                            <span class="text-sm text-slate-200">Current Session</span>
                                                        </div>
                                                        <span class="text-xs bg-green-900/30 text-green-400 px-2 py-0.5 rounded-full">Active</span>
                                                    </div>
                                                    <p class="text-xs text-slate-400">Last login: <span id="lastLoginTime">Loading...</span></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Data Management Tab (hidden by default) -->
                            <div id="profileContentData" class="profile-tab-pane hidden">
                                <div class="space-y-6">
                                    <!-- Data Management Card -->
                                    <div class="bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden">
                                        <div class="px-4 py-3 bg-slate-700/30 border-b border-slate-700/50">
                                            <h3 class="text-md font-medium text-slate-200 flex items-center">
                                                <i data-lucide="database" class="h-4 w-4 mr-2 text-cyan-400"></i>
                                                Data Management
                                            </h3>
                                        </div>
                                        <div class="p-4">
                                            <div class="bg-amber-900/20 border border-amber-700/50 rounded-md p-3 mb-4">
                                                <div class="flex items-start">
                                                    <i data-lucide="alert-triangle" class="h-5 w-5 text-amber-400 mr-2 mt-0.5 flex-shrink-0"></i>
                                                    <p class="text-sm text-amber-200">The following actions are permanent and cannot be undone. Please proceed with caution.</p>
                                                </div>
                                            </div>

                                            <div class="space-y-4">
                                                <!-- Clear Chat Threads -->
                                                <div class="bg-slate-700/30 rounded-lg p-4 border border-slate-600/50">
                                                    <div class="flex items-start mb-2">
                                                        <i data-lucide="message-square" class="h-5 w-5 text-slate-400 mr-2 mt-0.5 flex-shrink-0"></i>
                                                        <div>
                                                            <h4 class="text-sm font-medium text-slate-200 mb-1">Chat Threads</h4>
                                                            <p class="text-xs text-slate-400 mb-3">This will permanently delete all your chat threads from /chat.</p>
                                                        </div>
                                                    </div>
                                                    <button id="clearThreadsBtn" class="w-full bg-red-900/30 hover:bg-red-900/50 text-red-300 border border-red-900/50 rounded-md px-4 py-2 flex items-center justify-center">
                                                        <i data-lucide="trash-2" class="h-4 w-4 mr-2"></i>
                                                        Clear All Chat Threads
                                                    </button>
                                                </div>

                                                <!-- Clear Live Rooms -->
                                                <div class="bg-slate-700/30 rounded-lg p-4 border border-slate-600/50">
                                                    <div class="flex items-start mb-2">
                                                        <i data-lucide="users" class="h-5 w-5 text-slate-400 mr-2 mt-0.5 flex-shrink-0"></i>
                                                        <div>
                                                            <h4 class="text-sm font-medium text-slate-200 mb-1">Live Rooms</h4>
                                                            <p class="text-xs text-slate-400 mb-3">This will permanently delete all rooms you've created in /live.</p>
                                                        </div>
                                                    </div>
                                                    <button id="clearRoomsBtn" class="w-full bg-red-900/30 hover:bg-red-900/50 text-red-300 border border-red-900/50 rounded-md px-4 py-2 flex items-center justify-center">
                                                        <i data-lucide="trash-2" class="h-4 w-4 mr-2"></i>
                                                        Clear All Live Rooms
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Export Data -->
                                    <div class="bg-slate-800/50 rounded-lg border border-slate-700/50 overflow-hidden">
                                        <div class="px-4 py-3 bg-slate-700/30 border-b border-slate-700/50">
                                            <h3 class="text-md font-medium text-slate-200 flex items-center">
                                                <i data-lucide="download" class="h-4 w-4 mr-2 text-cyan-400"></i>
                                                Export Your Data
                                            </h3>
                                        </div>
                                        <div class="p-4">
                                            <p class="text-sm text-slate-300 mb-3">Download a copy of your data from Kevko Systems.</p>
                                            <button id="exportDataBtn" class="bg-slate-700 hover:bg-slate-600 text-slate-200 rounded-md px-4 py-2 flex items-center justify-center mb-3">
                                                <i data-lucide="download" class="h-4 w-4 mr-2"></i>
                                                Request Data Export
                                            </button>

                                            <div class="border-t border-slate-700/30 pt-3 mt-3">
                                                <p class="text-sm text-red-400 mb-3">Delete all your data from Kevko Systems. This action cannot be undone.</p>
                                                <button id="requestDataDeletionBtn" class="bg-red-900/30 hover:bg-red-900/50 text-red-300 border border-red-900/50 rounded-md px-4 py-2 flex items-center justify-center">
                                                    <i data-lucide="trash-2" class="h-4 w-4 mr-2"></i>
                                                    Request Data Deletion
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact View -->
                <div id="contactView" class="view-content hidden">
                    <div class="card p-6">
                        <div class="flex items-center mb-6">
                            <i data-lucide="phone" class="h-5 w-5 text-cyan-500 mr-2"></i>
                            <h2 class="text-xl font-semibold text-slate-100">Contact Admins</h2>
                        </div>
                        <p class="text-slate-400 mb-6">Need help with Kevko Systems? Contact one of our administrators below:</p>

                        <!-- Admin Contacts Container -->
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4" id="adminContactsContainer">
                            <!-- Admin contacts will be loaded dynamically -->
                            <div class="flex justify-center items-center h-40">
                                <div class="flex items-center">
                                    <i data-lucide="loader" class="h-5 w-5 text-slate-400 animate-spin mr-2"></i>
                                    <span class="text-slate-400">Loading admin contacts...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics View -->
                {% include 'includes/statistics_view.html' %}
            </div>
        </div>
    </div>

    <!-- Include Feature Restriction Modal -->
    {% include 'includes/feature_restriction_modal.html' %}

    <!-- Include Feature Restriction List Modal -->
    {% include 'includes/feature_restriction_list_modal.html' %}

    <!-- Include Access Control Modal -->
    {% include 'includes/access_control_modal.html' %}

    <!-- Include Access Control List Modal -->
    {% include 'includes/access_control_list_modal.html' %}

    <!-- Include Service Details Modal -->
    {% include 'includes/service_details_modal.html' %}

    <!-- Include Logs Modal -->
    {% include 'includes/logs_modal.html' %}

    <!-- Include Clear Logs Modal -->
    {% include 'includes/clear_logs_modal.html' %}

    <!-- Feature Restriction JS -->
    <script src="/static/js/FeatureRestrictions.js"></script>

    <!-- Access Control JS -->
    <script src="/static/js/AccessControl.js"></script>

    <!-- Admin Check JS -->
    <script src="/static/js/AdminCheck.js"></script>

    <!-- Admin Logs JS -->
    <script src="/static/js/AdminLogs.js"></script>

    <!-- Profile JS -->
    <script src="/static/js/Profile.js"></script>
    
    <!-- Modal Position Fix JS -->
    <script src="/static/js/modal-position-fix.js"></script>

    <script>
        // Set admin status for statistics panel
        window.isAdmin = {{ 'true' if current_user.is_admin else 'false' }};
        window.isSuperAdmin = {{ 'true' if current_user.is_superadmin else 'false' }};
    </script>

    <!-- Friends Panel CSS and JS -->
    <link rel="stylesheet" href="/static/css/friends-panel.css">
    <link rel="stylesheet" href="/static/css/friends-loading.css">
    <script src="/static/js/FriendsPanel.js"></script>
    <script src="/static/js/friends-loading.js"></script>

    <!-- Statistics Panel JS -->
    <script src="/static/js/StatisticsPanel.js"></script>

    <!-- Admin Panel Fix JS -->
    <script src="/static/js/fix-admin-panel-enhancements.js"></script>

    <!-- Admin Panel Settings Fix JS -->
    <script src="/static/js/admin-panel-fix.js"></script>

    <!-- Admin Updates JS -->
    <script src="/static/js/AdminUpdates.js"></script>

    <!-- Modal CSS Fix -->
    <link rel="stylesheet" href="/static/css/modal-fix.css">

    <!-- Credit Manager JS -->
    <script src="/static/js/CreditManager.js"></script>

    <!-- Credit Notification JS -->
    <script src="/static/js/CreditNotification.js"></script>
    <script src="/static/js/add-credits-modal-fix.js"></script>
    <script src="/static/js/credit-notification-fix.js"></script>
    <script src="/static/js/modal-test.js"></script>


    <!-- Friend-related Modals -->
    <!-- Add Friend Modal -->
    <div id="addFriendModal" class="modal fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 hidden">
        <div class="modal-content bg-slate-800/90 rounded-lg shadow-xl w-full max-w-md mx-4 border border-slate-700/50">
            <div class="px-5 py-4 border-b border-slate-700/70 flex justify-between items-center">
                <h3 class="text-lg font-medium text-slate-200 flex items-center">
                    <i data-lucide="user-plus" class="h-5 w-5 mr-2 text-cyan-400"></i>
                    Add Friend
                </h3>
                <button class="close-modal text-slate-400 hover:text-slate-200 p-1 rounded-full hover:bg-slate-700/50 transition-colors">
                    <i data-lucide="x" class="h-5 w-5"></i>
                </button>
            </div>
            <div class="p-5">
                <p class="text-slate-300 mb-5">Search for users by username to add them as friends.</p>
                <div class="flex items-center space-x-2 mb-5">
                    <div class="relative flex-grow">
                        <input type="text" id="userSearchInput" placeholder="Enter username... (>=3 Characters)" class="w-full bg-slate-700/70 border border-slate-600/70 rounded-lg px-4 py-3 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500/50 pl-10 shadow-inner shadow-black/10 transition-all">
                        <i data-lucide="search" class="h-4 w-4 text-slate-400 absolute left-4 top-3.5"></i>
                    </div>
                    <button id="searchUsersBtn" class="friend-request-btn bg-cyan-600 hover:bg-cyan-500 text-white rounded-lg px-4 py-3 flex-shrink-0 shadow-md shadow-cyan-500/10 transition-all">
                        <i data-lucide="search" class="h-5 w-5"></i>
                    </button>
                </div>
                <div id="searchResults" class="space-y-3 max-h-[300px] overflow-y-auto pr-1">
                    <!-- Search results will be dynamically inserted here -->
                    <div class="empty-state py-8">
                        <div class="empty-state-icon">
                            <i data-lucide="users" class="h-6 w-6 text-slate-500"></i>
                        </div>
                        <p class="empty-state-text text-sm text-slate-400 mt-3">Search for users by their username to add them as friends</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Friend Requests Modal -->
    <div id="friendRequestsModal" class="modal fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 hidden">
        <div class="modal-content bg-slate-800/90 rounded-lg shadow-xl w-full max-w-md mx-4 border border-slate-700/50">
            <div class="px-5 py-4 border-b border-slate-700/70 flex justify-between items-center">
                <h3 class="text-lg font-medium text-slate-200 flex items-center">
                    <i data-lucide="users" class="h-5 w-5 mr-2 text-cyan-400"></i>
                    Friend Requests
                </h3>
                <button class="close-modal text-slate-400 hover:text-slate-200 p-1 rounded-full hover:bg-slate-700/50 transition-colors">
                    <i data-lucide="x" class="h-5 w-5"></i>
                </button>
            </div>
            <div class="p-5">
                <div class="flex border-b border-slate-700/70 mb-5">
                    <button class="tab active relative px-5 py-3 text-slate-200 font-medium focus:outline-none transition-colors" data-tab="pending">
                        <span class="flex items-center">
                            <i data-lucide="inbox" class="h-4 w-4 mr-2"></i>
                            Pending
                        </span>
                        <span class="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-cyan-500 to-blue-500 transform scale-x-100 transition-transform origin-left"></span>
                    </button>
                    <button class="tab relative px-5 py-3 text-slate-400 font-medium focus:outline-none transition-colors" data-tab="sent">
                        <span class="flex items-center">
                            <i data-lucide="send" class="h-4 w-4 mr-2"></i>
                            Sent
                        </span>
                        <span class="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-cyan-500 to-blue-500 transform scale-x-0 transition-transform origin-left"></span>
                    </button>
                </div>
                <div id="pendingRequestsTab" class="tab-content">
                    <div id="pendingRequests" class="space-y-3 max-h-[300px] overflow-y-auto pr-1">
                        <!-- Pending requests will be dynamically inserted here -->
                        <div class="flex justify-center items-center py-6">
                            <div class="flex items-center">
                                <i data-lucide="loader" class="h-5 w-5 text-slate-500 animate-spin mr-2"></i>
                                <span class="text-slate-500 text-sm">Loading requests...</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="sentRequestsTab" class="tab-content hidden">
                    <div id="sentRequests" class="space-y-3 max-h-[300px] overflow-y-auto pr-1">
                        <!-- Sent requests will be dynamically inserted here -->
                        <div class="flex justify-center items-center py-6">
                            <div class="flex items-center">
                                <i data-lucide="loader" class="h-5 w-5 text-slate-500 animate-spin mr-2"></i>
                                <span class="text-slate-500 text-sm">Loading requests...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Remove Friend Confirmation Modal -->
    <div id="removeFriendModal" class="modal fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 hidden">
        <div class="modal-content bg-slate-800/90 rounded-lg shadow-xl w-full max-w-md mx-4 border border-slate-700/50">
            <div class="px-5 py-4 border-b border-slate-700/70 flex justify-between items-center">
                <h3 class="text-lg font-medium text-slate-200 flex items-center">
                    <i data-lucide="user-minus" class="h-5 w-5 mr-2 text-red-400"></i>
                    Remove Friend
                </h3>
                <button class="close-modal text-slate-400 hover:text-slate-200 p-1 rounded-full hover:bg-slate-700/50 transition-colors">
                    <i data-lucide="x" class="h-5 w-5"></i>
                </button>
            </div>
            <div class="p-5">
                <div class="bg-red-900/20 border border-red-900/30 rounded-lg p-4 mb-5">
                    <div class="flex items-start">
                        <i data-lucide="alert-triangle" class="h-5 w-5 text-red-400 mr-3 mt-0.5 flex-shrink-0"></i>
                        <p class="text-sm text-red-300">
                            Are you sure you want to remove <span id="removeFriendName" class="font-medium"></span> from your friends? This action cannot be undone.
                        </p>
                    </div>
                </div>
                <div class="flex justify-end space-x-3">
                    <button id="cancelRemoveBtn" class="friend-action-btn px-4 py-2 bg-slate-700 hover:bg-slate-600 text-slate-200 rounded-md shadow-md shadow-black/10 transition-all">
                        Cancel
                    </button>
                    <button id="confirmRemoveBtn" class="friend-action-btn px-4 py-2 bg-red-600 hover:bg-red-500 text-white rounded-md shadow-md shadow-red-500/10 transition-all">
                        <i data-lucide="user-minus" class="h-4 w-4 mr-2 inline"></i>
                        Remove
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Group Chat Modal -->
    <div id="createGroupChatModal" class="modal fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 hidden">
        <div class="modal-content bg-slate-800/90 rounded-lg shadow-xl w-full max-w-md mx-4 border border-slate-700/50">
            <div class="px-5 py-4 border-b border-slate-700/70 flex justify-between items-center">
                <h3 class="text-lg font-medium text-slate-200 flex items-center">
                    <i data-lucide="users-round" class="h-5 w-5 mr-2 text-purple-400"></i>
                    Create Group Chat
                </h3>
                <button class="close-modal text-slate-400 hover:text-slate-200 p-1 rounded-full hover:bg-slate-700/50 transition-colors">
                    <i data-lucide="x" class="h-5 w-5"></i>
                </button>
            </div>
            <div class="p-5">
                <div class="mb-4">
                    <label for="groupChatName" class="block text-sm font-medium text-slate-300 mb-1">Group Name</label>
                    <input type="text" id="groupChatName" placeholder="Enter group name..." class="w-full bg-slate-700/70 border border-slate-600/70 rounded-md px-3 py-2 text-slate-200 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500/50 transition-all">
                </div>
                <div class="mb-4">
                    <label class="block text-sm font-medium text-slate-300 mb-1">Select Friends</label>
                    <div class="bg-slate-700/50 border border-slate-600/50 rounded-md p-2 max-h-60 overflow-y-auto" id="friendSelectList">
                        <!-- Friends will be loaded here -->
                        <div class="flex justify-center items-center py-4">
                            <div class="flex items-center">
                                <i data-lucide="loader" class="h-4 w-4 text-slate-500 animate-spin mr-2"></i>
                                <span class="text-slate-500 text-sm">Loading friends...</span>
                            </div>
                        </div>
                    </div>
                    <p class="text-xs text-slate-400 mt-1">Select up to 6 friends to add to the group</p>
                </div>
                <div class="flex justify-end">
                    <button id="createGroupChatSubmitBtn" class="px-4 py-2 bg-purple-600 hover:bg-purple-500 text-white rounded-md text-sm font-medium shadow-md shadow-purple-500/10 transition-all disabled:opacity-50 disabled:cursor-not-allowed">
                        <i data-lucide="users-round" class="h-4 w-4 inline-block mr-1"></i>
                        Create Group Chat
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Invite to Live Modal -->
    <div id="inviteToLiveModal" class="modal fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 hidden">
        <div class="modal-content bg-slate-800/90 rounded-lg shadow-xl w-full max-w-md mx-4 border border-slate-700/50">
            <div class="px-5 py-4 border-b border-slate-700/70 flex justify-between items-center">
                <h3 class="text-lg font-medium text-slate-200 flex items-center">
                    <i data-lucide="video" class="h-5 w-5 mr-2 text-cyan-400"></i>
                    Invite to Live Chat
                </h3>
                <button class="close-modal text-slate-400 hover:text-slate-200 p-1 rounded-full hover:bg-slate-700/50 transition-colors">
                    <i data-lucide="x" class="h-5 w-5"></i>
                </button>
            </div>
            <div class="p-5">
                <div class="bg-cyan-900/20 border border-cyan-900/30 rounded-lg p-4 mb-5">
                    <div class="flex items-start">
                        <i data-lucide="info" class="h-5 w-5 text-cyan-400 mr-3 mt-0.5 flex-shrink-0"></i>
                        <p class="text-sm text-cyan-300">
                            Create a new live chat room and invite <span id="inviteFriendName" class="font-medium"></span> to join? A notification will be sent to your friend.
                        </p>
                    </div>
                </div>
                <div class="flex justify-end space-x-3">
                    <button id="cancelInviteBtn" class="friend-action-btn px-4 py-2 bg-slate-700 hover:bg-slate-600 text-slate-200 rounded-md shadow-md shadow-black/10 transition-all">
                        Cancel
                    </button>
                    <button id="confirmInviteBtn" class="friend-action-btn px-4 py-2 bg-cyan-600 hover:bg-cyan-500 text-white rounded-md shadow-md shadow-cyan-500/10 transition-all">
                        <i data-lucide="video" class="h-4 w-4 mr-2 inline"></i>
                        Create Room
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Group Chat Modal -->
    <div id="deleteGroupChatModal" class="modal fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 hidden">
        <div class="modal-content bg-slate-800/90 rounded-lg shadow-xl w-full max-w-md mx-4 border border-slate-700/50">
            <div class="px-5 py-4 border-b border-slate-700/70 flex justify-between items-center">
                <h3 class="text-lg font-medium text-slate-200 flex items-center">
                    <i data-lucide="trash-2" class="h-5 w-5 mr-2 text-red-400"></i>
                    Delete Group Chat
                </h3>
                <button class="close-modal text-slate-400 hover:text-slate-200 p-1 rounded-full hover:bg-slate-700/50 transition-colors">
                    <i data-lucide="x" class="h-5 w-5"></i>
                </button>
            </div>
            <div class="p-5">
                <div class="bg-red-900/20 border border-red-900/30 rounded-lg p-4 mb-5">
                    <div class="flex items-start">
                        <i data-lucide="alert-triangle" class="h-5 w-5 text-red-400 mr-3 mt-0.5 flex-shrink-0"></i>
                        <p class="text-sm text-red-300">
                            As the creator of this group chat, are you sure you want to delete "<span id="deleteGroupChatName" class="font-medium"></span>"? This action cannot be undone and will remove the chat for all members.
                        </p>
                    </div>
                </div>
                <div class="flex justify-end space-x-3">
                    <button id="cancelDeleteGroupBtn" class="friend-action-btn px-4 py-2 bg-slate-700 hover:bg-slate-600 text-slate-200 rounded-md shadow-md shadow-black/10 transition-all">
                        Cancel
                    </button>
                    <button id="confirmDeleteGroupBtn" class="friend-action-btn px-4 py-2 bg-red-600 hover:bg-red-500 text-white rounded-md shadow-md shadow-red-500/10 transition-all">
                        <i data-lucide="trash-2" class="h-4 w-4 mr-2 inline"></i>
                        Delete Group Chat
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Include Group Info Modal -->
    {% include 'includes/group_info_modal.html' %}

    <!-- Make sure NotificationManager is loaded -->
    <script>
        // Check if NotificationManager exists, if not, load it
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                if (typeof window.notificationManager === 'undefined') {
                    const script = document.createElement('script');
                    script.src = '/static/js/NotificationManager.js';
                    document.body.appendChild(script);
                }
            }, 1000);
        });
    </script>

    <!-- Image Viewer Modal -->
    <div id="imageViewerModal" class="modal fixed inset-0 bg-black/90 backdrop-blur-sm flex items-center justify-center z-50 hidden">
        <div class="modal-content bg-transparent w-full h-full flex flex-col items-center justify-center p-4">
            <div class="absolute top-4 right-4 z-10">
                <button id="closeImageViewerBtn" class="text-white hover:text-slate-200 p-2 rounded-full hover:bg-slate-800/50 transition-colors">
                    <i data-lucide="x" class="h-6 w-6"></i>
                </button>
            </div>
            <div class="w-full h-full flex items-center justify-center">
                <img id="fullSizeImage" src="" alt="Full size image" class="max-w-full max-h-[90vh] object-contain">
            </div>
        </div>
    </div>

    <!-- Rename Group Chat Modal -->
    <div id="renameGroupChatModal" class="modal fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 hidden">
        <div class="modal-content bg-slate-800/90 rounded-lg shadow-xl w-full max-w-md mx-4 border border-slate-700/50">
            <div class="px-5 py-4 border-b border-slate-700/70 flex justify-between items-center">
                <h3 class="text-lg font-medium text-slate-200 flex items-center">
                    <i data-lucide="edit-3" class="h-5 w-5 mr-2 text-cyan-400"></i>
                    Rename Group Chat
                </h3>
                <button class="close-modal text-slate-400 hover:text-slate-200 p-1 rounded-full hover:bg-slate-700/50 transition-colors">
                    <i data-lucide="x" class="h-5 w-5"></i>
                </button>
            </div>
            <div class="p-5">
                <div class="mb-4">
                    <label for="newGroupChatName" class="block text-sm font-medium text-slate-300 mb-1">New Group Name</label>
                    <input type="text" id="newGroupChatName" placeholder="Enter new group name..." class="w-full bg-slate-700/70 border border-slate-600/70 rounded-md px-3 py-2 text-slate-200 text-sm focus:outline-none focus:ring-2 focus:ring-cyan-500/50 transition-all">
                </div>
                <div class="flex justify-end space-x-3">
                    <button id="cancelRenameGroupBtn" class="friend-action-btn px-4 py-2 bg-slate-700 hover:bg-slate-600 text-slate-200 rounded-md shadow-md shadow-black/10 transition-all">
                        Cancel
                    </button>
                    <button id="confirmRenameGroupBtn" class="friend-action-btn px-4 py-2 bg-cyan-600 hover:bg-cyan-500 text-white rounded-md shadow-md shadow-cyan-500/10 transition-all">
                        <i data-lucide="save" class="h-4 w-4 mr-2 inline"></i>
                        Save
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Friends Menu Modal -->
    <div id="friendsMenuModal" class="modal hidden fixed inset-0 z-50 overflow-auto bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center">
        <div class="modal-content bg-gradient-to-br from-slate-800 to-slate-900 rounded-xl shadow-2xl w-80 transform scale-95 opacity-0 transition-all duration-300 border border-slate-700/50">
            <div class="p-5">
                <div class="flex justify-between items-center mb-5">
                    <h3 class="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-400 to-indigo-500 flex items-center">
                        <i data-lucide="users" class="h-5 w-5 mr-2 text-cyan-400"></i>
                        Friends Options
                    </h3>
                    <button class="close-modal text-slate-400 hover:text-white p-1.5 rounded-full hover:bg-slate-700/50 transition-colors">
                        <i data-lucide="x" class="h-4 w-4"></i>
                    </button>
                </div>

                <div class="grid grid-cols-2 gap-3 mb-3">
                    <button id="modalAddFriendBtn" class="group bg-gradient-to-br from-cyan-600 to-cyan-700 hover:from-cyan-500 hover:to-cyan-600 text-white rounded-lg p-3 flex flex-col items-center justify-center transition-all shadow-md hover:shadow-cyan-500/20 hover:-translate-y-0.5">
                        <div class="bg-cyan-500/20 p-2 rounded-lg mb-2 group-hover:bg-cyan-500/30 transition-colors">
                            <i data-lucide="user-plus" class="h-5 w-5"></i>
                        </div>
                        <span class="text-sm font-medium">Add Friend</span>
                    </button>

                    <button id="modalCreateGroupChatBtn" class="group bg-gradient-to-br from-purple-600 to-purple-700 hover:from-purple-500 hover:to-purple-600 text-white rounded-lg p-3 flex flex-col items-center justify-center transition-all shadow-md hover:shadow-purple-500/20 hover:-translate-y-0.5">
                        <div class="bg-purple-500/20 p-2 rounded-lg mb-2 group-hover:bg-purple-500/30 transition-colors">
                            <i data-lucide="users-round" class="h-5 w-5"></i>
                        </div>
                        <span class="text-sm font-medium">Create Group Chat</span>
                    </button>
                </div>

                <div class="grid grid-cols-2 gap-3">
                    <button id="modalPendingRequestsBtn" class="group bg-gradient-to-br from-amber-600 to-amber-700 hover:from-amber-500 hover:to-amber-600 text-white rounded-lg p-3 flex flex-col items-center justify-center transition-all shadow-md hover:shadow-amber-500/20 hover:-translate-y-0.5 relative">
                        <div class="bg-amber-500/20 p-2 rounded-lg mb-2 group-hover:bg-amber-500/30 transition-colors">
                            <i data-lucide="users" class="h-5 w-5"></i>
                        </div>
                        <span class="text-sm font-medium">Friend Requests</span>
                        <span id="modalRequestBadge" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center hidden shadow-md">0</span>
                    </button>

                    <button id="modalChatThemeBtn" class="group bg-gradient-to-br from-indigo-600 to-blue-700 hover:from-indigo-500 hover:to-blue-600 text-white rounded-lg p-3 flex flex-col items-center justify-center transition-all shadow-md hover:shadow-indigo-500/20 hover:-translate-y-0.5">
                        <div class="bg-indigo-500/20 p-2 rounded-lg mb-2 group-hover:bg-indigo-500/30 transition-colors">
                            <i data-lucide="palette" class="h-5 w-5"></i>
                        </div>
                        <span class="text-sm font-medium">Chat Theme</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Theme Modal -->
    <div id="chatThemeModal" class="modal hidden fixed inset-0 z-50 overflow-auto bg-black bg-opacity-70 backdrop-blur-md flex items-center justify-center">
        <div class="modal-content bg-gradient-to-br from-slate-800 to-slate-900 rounded-xl shadow-2xl w-full max-w-xl mx-4 transform scale-95 opacity-0 transition-all duration-300 border border-slate-700/50">
            <div class="p-4 border-b border-slate-700/50">
                <div class="flex justify-between items-center">
                    <h3 class="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-blue-500 flex items-center">
                        <i data-lucide="palette" class="h-5 w-5 mr-2 text-indigo-400"></i>
                        Select Chat Theme
                    </h3>
                    <button class="close-modal text-slate-400 hover:text-white p-1.5 rounded-full hover:bg-slate-700/50 transition-colors">
                        <i data-lucide="x" class="h-4 w-4"></i>
                    </button>
                </div>
                <p class="text-slate-400 text-sm mt-1">Personalize your chat experience with these custom themes</p>
            </div>
            <div class="p-4">
                <!-- Theme Carousel Container -->
                <div class="theme-carousel-container">
                    <!-- Navigation Controls -->
                    <div class="theme-carousel-nav">
                        <button class="nav-button prev-btn" id="prevThemeBtn">
                            <i data-lucide="chevron-left" class="h-5 w-5"></i>
                        </button>
                        <button class="nav-button next-btn" id="nextThemeBtn">
                            <i data-lucide="chevron-right" class="h-5 w-5"></i>
                        </button>
                    </div>

                    <!-- Theme Carousel -->
                    <div class="theme-carousel" id="themeCarousel">
                        <!-- Blue Theme (Default) -->
                        <div class="theme-card" data-theme="blue">
                            <div class="theme-card-content">
                                <div class="theme-preview bg-gradient-to-b from-slate-900 to-slate-950 p-3 relative">
                                    <div class="absolute top-2 right-2 theme-selected-indicator bg-indigo-500 text-white rounded-full p-1 opacity-0 transition-opacity shadow-md">
                                        <i data-lucide="check" class="h-3 w-3"></i>
                                    </div>
                                    <div class="message-preview self flex justify-end mb-2">
                                        <div class="px-3 py-2 rounded-lg bg-indigo-600 text-white text-xs max-w-[80%] shadow-sm">
                                            Hello there!
                                        </div>
                                    </div>
                                    <div class="message-preview friend">
                                        <div class="px-3 py-2 rounded-lg bg-slate-800 text-white text-xs max-w-[80%] shadow-sm">
                                            Hi, how are you?
                                        </div>
                                    </div>
                                </div>
                                <div class="p-2 bg-gradient-to-r from-indigo-600 to-blue-600 text-center">
                                    <span class="text-sm font-medium text-white">Blue (Default)</span>
                                </div>
                            </div>
                        </div>

                        <!-- Purple Theme -->
                        <div class="theme-card" data-theme="purple">
                            <div class="theme-card-content">
                                <div class="theme-preview bg-gradient-to-b from-slate-900 to-slate-950 p-3 relative">
                                    <div class="absolute top-2 right-2 theme-selected-indicator bg-indigo-500 text-white rounded-full p-1 opacity-0 transition-opacity shadow-md">
                                        <i data-lucide="check" class="h-3 w-3"></i>
                                    </div>
                                    <div class="message-preview self flex justify-end mb-2">
                                        <div class="px-3 py-2 rounded-lg bg-purple-600 text-white text-xs max-w-[80%] shadow-sm">
                                            Hello there!
                                        </div>
                                    </div>
                                    <div class="message-preview friend">
                                        <div class="px-3 py-2 rounded-lg bg-slate-800 text-white text-xs max-w-[80%] shadow-sm">
                                            Hi, how are you?
                                        </div>
                                    </div>
                                </div>
                                <div class="p-2 bg-gradient-to-r from-purple-600 to-fuchsia-600 text-center">
                                    <span class="text-sm font-medium text-white">Purple</span>
                                </div>
                            </div>
                        </div>

                        <!-- Green Theme -->
                        <div class="theme-card" data-theme="green">
                            <div class="theme-card-content">
                                <div class="theme-preview bg-gradient-to-b from-slate-900 to-slate-950 p-3 relative">
                                    <div class="absolute top-2 right-2 theme-selected-indicator bg-indigo-500 text-white rounded-full p-1 opacity-0 transition-opacity shadow-md">
                                        <i data-lucide="check" class="h-3 w-3"></i>
                                    </div>
                                    <div class="message-preview self flex justify-end mb-2">
                                        <div class="px-3 py-2 rounded-lg bg-emerald-600 text-white text-xs max-w-[80%] shadow-sm">
                                            Hello there!
                                        </div>
                                    </div>
                                    <div class="message-preview friend">
                                        <div class="px-3 py-2 rounded-lg bg-slate-800 text-white text-xs max-w-[80%] shadow-sm">
                                            Hi, how are you?
                                        </div>
                                    </div>
                                </div>
                                <div class="p-2 bg-gradient-to-r from-emerald-600 to-green-600 text-center">
                                    <span class="text-sm font-medium text-white">Green</span>
                                </div>
                            </div>
                        </div>

                        <!-- Red Theme -->
                        <div class="theme-card" data-theme="red">
                            <div class="theme-card-content">
                                <div class="theme-preview bg-gradient-to-b from-slate-900 to-slate-950 p-3 relative">
                                    <div class="absolute top-2 right-2 theme-selected-indicator bg-indigo-500 text-white rounded-full p-1 opacity-0 transition-opacity shadow-md">
                                        <i data-lucide="check" class="h-3 w-3"></i>
                                    </div>
                                    <div class="message-preview self flex justify-end mb-2">
                                        <div class="px-3 py-2 rounded-lg bg-red-600 text-white text-xs max-w-[80%] shadow-sm">
                                            Hello there!
                                        </div>
                                    </div>
                                    <div class="message-preview friend">
                                        <div class="px-3 py-2 rounded-lg bg-slate-800 text-white text-xs max-w-[80%] shadow-sm">
                                            Hi, how are you?
                                        </div>
                                    </div>
                                </div>
                                <div class="p-2 bg-gradient-to-r from-red-600 to-rose-600 text-center">
                                    <span class="text-sm font-medium text-white">Red</span>
                                </div>
                            </div>
                        </div>

                        <!-- Orange Theme -->
                        <div class="theme-card" data-theme="orange">
                            <div class="theme-card-content">
                                <div class="theme-preview bg-gradient-to-b from-slate-900 to-slate-950 p-3 relative">
                                    <div class="absolute top-2 right-2 theme-selected-indicator bg-indigo-500 text-white rounded-full p-1 opacity-0 transition-opacity shadow-md">
                                        <i data-lucide="check" class="h-3 w-3"></i>
                                    </div>
                                    <div class="message-preview self flex justify-end mb-2">
                                        <div class="px-3 py-2 rounded-lg bg-orange-600 text-white text-xs max-w-[80%] shadow-sm">
                                            Hello there!
                                        </div>
                                    </div>
                                    <div class="message-preview friend">
                                        <div class="px-3 py-2 rounded-lg bg-slate-800 text-white text-xs max-w-[80%] shadow-sm">
                                            Hi, how are you?
                                        </div>
                                    </div>
                                </div>
                                <div class="p-2 bg-gradient-to-r from-orange-600 to-amber-600 text-center">
                                    <span class="text-sm font-medium text-white">Orange</span>
                                </div>
                            </div>
                        </div>

                        <!-- Teal Theme -->
                        <div class="theme-card" data-theme="teal">
                            <div class="theme-card-content">
                                <div class="theme-preview bg-gradient-to-b from-slate-900 to-slate-950 p-3 relative">
                                    <div class="absolute top-2 right-2 theme-selected-indicator bg-indigo-500 text-white rounded-full p-1 opacity-0 transition-opacity shadow-md">
                                        <i data-lucide="check" class="h-3 w-3"></i>
                                    </div>
                                    <div class="message-preview self flex justify-end mb-2">
                                        <div class="px-3 py-2 rounded-lg bg-teal-600 text-white text-xs max-w-[80%] shadow-sm">
                                            Hello there!
                                        </div>
                                    </div>
                                    <div class="message-preview friend">
                                        <div class="px-3 py-2 rounded-lg bg-slate-800 text-white text-xs max-w-[80%] shadow-sm">
                                            Hi, how are you?
                                        </div>
                                    </div>
                                </div>
                                <div class="p-2 bg-gradient-to-r from-teal-600 to-cyan-600 text-center">
                                    <span class="text-sm font-medium text-white">Teal</span>
                                </div>
                            </div>
                        </div>

                        <!-- Pink Theme -->
                        <div class="theme-card" data-theme="pink">
                            <div class="theme-card-content">
                                <div class="theme-preview bg-gradient-to-b from-slate-900 to-slate-950 p-3 relative">
                                    <div class="absolute top-2 right-2 theme-selected-indicator bg-indigo-500 text-white rounded-full p-1 opacity-0 transition-opacity shadow-md">
                                        <i data-lucide="check" class="h-3 w-3"></i>
                                    </div>
                                    <div class="message-preview self flex justify-end mb-2">
                                        <div class="px-3 py-2 rounded-lg bg-pink-600 text-white text-xs max-w-[80%] shadow-sm">
                                            Hello there!
                                        </div>
                                    </div>
                                    <div class="message-preview friend">
                                        <div class="px-3 py-2 rounded-lg bg-slate-800 text-white text-xs max-w-[80%] shadow-sm">
                                            Hi, how are you?
                                        </div>
                                    </div>
                                </div>
                                <div class="p-2 bg-gradient-to-r from-pink-600 to-rose-500 text-center">
                                    <span class="text-sm font-medium text-white">Pink</span>
                                </div>
                            </div>
                        </div>

                        <!-- Indigo Theme -->
                        <div class="theme-card" data-theme="indigo">
                            <div class="theme-card-content">
                                <div class="theme-preview bg-gradient-to-b from-slate-900 to-slate-950 p-3 relative">
                                    <div class="absolute top-2 right-2 theme-selected-indicator bg-indigo-500 text-white rounded-full p-1 opacity-0 transition-opacity shadow-md">
                                        <i data-lucide="check" class="h-3 w-3"></i>
                                    </div>
                                    <div class="message-preview self flex justify-end mb-2">
                                        <div class="px-3 py-2 rounded-lg bg-indigo-500 text-white text-xs max-w-[80%] shadow-sm">
                                            Hello there!
                                        </div>
                                    </div>
                                    <div class="message-preview friend">
                                        <div class="px-3 py-2 rounded-lg bg-slate-800 text-white text-xs max-w-[80%] shadow-sm">
                                            Hi, how are you?
                                        </div>
                                    </div>
                                </div>
                                <div class="p-2 bg-gradient-to-r from-indigo-500 to-violet-500 text-center">
                                    <span class="text-sm font-medium text-white">Indigo</span>
                                </div>
                            </div>
                        </div>

                        <!-- Amber Theme -->
                        <div class="theme-card" data-theme="amber">
                            <div class="theme-card-content">
                                <div class="theme-preview bg-gradient-to-b from-slate-900 to-slate-950 p-3 relative">
                                    <div class="absolute top-2 right-2 theme-selected-indicator bg-indigo-500 text-white rounded-full p-1 opacity-0 transition-opacity shadow-md">
                                        <i data-lucide="check" class="h-3 w-3"></i>
                                    </div>
                                    <div class="message-preview self flex justify-end mb-2">
                                        <div class="px-3 py-2 rounded-lg bg-amber-500 text-white text-xs max-w-[80%] shadow-sm">
                                            Hello there!
                                        </div>
                                    </div>
                                    <div class="message-preview friend">
                                        <div class="px-3 py-2 rounded-lg bg-slate-800 text-white text-xs max-w-[80%] shadow-sm">
                                            Hi, how are you?
                                        </div>
                                    </div>
                                </div>
                                <div class="p-2 bg-gradient-to-r from-amber-500 to-yellow-500 text-center">
                                    <span class="text-sm font-medium text-white">Amber</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Theme Indicators -->
                    <div class="theme-indicators" id="themeIndicators">
                        <!-- Will be populated by JavaScript -->
                    </div>
                </div>

                <div class="flex items-center justify-between border-t border-slate-700/50 pt-4 mt-2">
                    <button id="resetThemeBtn" class="text-slate-400 hover:text-slate-200 text-sm flex items-center transition-colors">
                        <i data-lucide="rotate-ccw" class="h-3.5 w-3.5 mr-1.5"></i>
                        Reset to Default
                    </button>

                    <div class="flex items-center space-x-2">
                        <span id="selectedThemeName" class="text-xs text-slate-400">Blue (Default)</span>
                        <button id="applyThemeBtn" class="px-4 py-1.5 bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-500 hover:to-blue-500 text-white text-sm font-medium rounded-md shadow-md hover:shadow-indigo-500/20 transition-all flex items-center">
                            <i data-lucide="check" class="h-3.5 w-3.5 mr-1.5"></i>
                            Apply Theme
                        </button>
                    </div>
                </div>


            </div>
        </div>
    </div>

    <!-- Avatar Upload Modal -->
    <div id="avatarUploadModal" class="fixed inset-0 z-50 flex items-center justify-center hidden">
        <div class="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"></div>
        <div class="relative bg-slate-800 rounded-lg shadow-xl w-full max-w-md mx-4 overflow-hidden">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-4 border-b border-slate-700">
                <h3 class="text-xl font-medium text-slate-100">
                    <i data-lucide="camera" class="h-5 w-5 inline mr-2 text-cyan-400"></i>
                    Change Profile Picture
                </h3>
                <button id="closeAvatarModal" class="text-slate-400 hover:text-slate-100">
                    <i data-lucide="x" class="h-5 w-5"></i>
                </button>
            </div>

            <!-- Modal Body -->
            <div class="p-6">
                <div class="mb-6">
                    <div class="flex justify-center mb-4">
                        <div id="avatarPreview" class="w-32 h-32 rounded-full bg-slate-700 flex items-center justify-center text-white text-3xl font-bold overflow-hidden">
                            <!-- Preview will be shown here -->
                            <span id="avatarPreviewInitials">KS</span>
                        </div>
                    </div>

                    <div class="text-center mb-4">
                        <p class="text-sm text-slate-400 mb-2">Upload a new profile picture</p>
                        <p class="text-xs text-slate-500">Supported formats: JPG, PNG, GIF, WEBP</p>
                    </div>

                    <form id="avatarUploadForm" enctype="multipart/form-data">
                        <div class="flex flex-col items-center">
                            <label for="avatarFile" class="cursor-pointer bg-slate-700 hover:bg-slate-600 text-slate-200 py-2 px-4 rounded-lg flex items-center transition-colors">
                                <i data-lucide="upload" class="h-4 w-4 mr-2"></i>
                                Select Image
                            </label>
                            <input type="file" id="avatarFile" name="avatar" accept="image/png, image/jpeg, image/gif, image/webp" class="hidden">
                            <p id="selectedFileName" class="mt-2 text-sm text-slate-400">No file selected</p>
                        </div>
                    </form>
                </div>

                <div class="flex justify-between">
                    <button id="cancelAvatarUpload" class="bg-slate-700 hover:bg-slate-600 text-slate-200 py-2 px-4 rounded-lg transition-colors">
                        Cancel
                    </button>
                    <button id="uploadAvatarBtn" class="bg-cyan-600 hover:bg-cyan-500 text-white py-2 px-4 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                        <span id="uploadBtnText">Upload</span>
                        <span id="uploadSpinner" class="hidden">
                            <i data-lucide="loader-2" class="h-4 w-4 animate-spin"></i>
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>

</body>
</html>
