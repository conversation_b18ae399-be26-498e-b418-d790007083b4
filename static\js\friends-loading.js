/**
 * Friends Loading Placeholders
 * Handles displaying and hiding loading placeholders for the Friends area
 */

// Configuration
const PLACEHOLDER_CONFIG = {
    friendItems: 8,     // Number of friend placeholders to show
    groupChats: 3,      // Number of group chat placeholders to show
    messages: 5,        // Number of message placeholders in chat area
    emptyState: true    // Whether to show empty state placeholder
};

// Main class to handle loading states
class FriendsLoadingManager {
    constructor(config = PLACEHOLDER_CONFIG) {
        this.config = config;
        this.isLoading = false;
        this.friendsLoaded = false;
        this.groupChatsLoaded = false;
        this.friendListContainer = document.querySelector('.friend-list') || document.getElementById('friendList');
        this.chatContainer = document.querySelector('.message-container') || document.getElementById('messagesContainer');

        // Create placeholder elements
        this.friendListPlaceholder = this.createFriendListPlaceholder();
        this.chatPlaceholder = this.createChatPlaceholder();

        // Initialize event listeners
        this.initEventListeners();
    }

    // Initialize event listeners
    initEventListeners() {
        // Listen for custom events that might trigger loading state
        document.addEventListener('friends:loading', () => this.showLoading());

        // Listen for friends loaded event
        document.addEventListener('friends:loaded', () => {
            console.log('Friends loaded event received');
            this.friendsLoaded = true;
            this.checkAndHideLoading();
        });

        // Listen for group chats loaded event
        document.addEventListener('group_chats:loaded', () => {
            console.log('Group chats loaded event received');
            this.groupChatsLoaded = true;
            this.checkAndHideLoading();
        });

        // Auto-hide loading after timeout (fallback)
        window.addEventListener('load', () => {
            // If page loads with empty friend list, show placeholders
            if (this.friendListContainer && this.friendListContainer.children.length === 0) {
                this.showLoading();
            }
        });

        // Fallback timeout to hide loading after 10 seconds regardless
        document.addEventListener('friends:loading', () => {
            setTimeout(() => {
                if (this.isLoading) {
                    console.log('Fallback timeout: hiding loading placeholders after 10 seconds');
                    this.hideLoading();
                }
            }, 10000);
        });
    }

    // Show loading placeholders
    showLoading() {
        if (this.isLoading) return;
        this.isLoading = true;
        this.friendsLoaded = false;
        this.groupChatsLoaded = false;

        console.log('Showing loading placeholders');

        // Show friend list placeholders
        if (this.friendListContainer) {
            // Store original content
            this.originalFriendListContent = this.friendListContainer.innerHTML;
            // Replace with placeholders
            this.friendListContainer.innerHTML = '';
            this.friendListContainer.appendChild(this.friendListPlaceholder);
        }

        // Show chat placeholders if chat container exists and is empty
        if (this.chatContainer && this.chatContainer.children.length === 0) {
            // Store original content
            this.originalChatContent = this.chatContainer.innerHTML;
            // Replace with placeholders
            this.chatContainer.innerHTML = '';
            this.chatContainer.appendChild(this.chatPlaceholder);
        }
    }

    // Check if both friends and group chats are loaded, then hide loading
    checkAndHideLoading() {
        console.log(`Checking loading state: Friends loaded: ${this.friendsLoaded}, Group chats loaded: ${this.groupChatsLoaded}`);
        if (this.friendsLoaded && this.groupChatsLoaded) {
            console.log('Both friends and group chats loaded, hiding placeholders');
            this.hideLoading();
        }
    }

    // Hide loading placeholders
    hideLoading() {
        if (!this.isLoading) return;

        console.log('Hiding loading placeholders');
        this.isLoading = false;

        // Restore friend list
        if (this.friendListContainer && this.friendListContainer.contains(this.friendListPlaceholder)) {
            // Don't restore original content - it will be replaced by the actual content
            this.friendListContainer.removeChild(this.friendListPlaceholder);
        }

        // Restore chat container
        if (this.chatContainer && this.chatContainer.contains(this.chatPlaceholder)) {
            // If we have original content, restore it
            if (this.originalChatContent) {
                this.chatContainer.innerHTML = this.originalChatContent;
            } else {
                // Otherwise just remove the placeholder
                this.chatContainer.removeChild(this.chatPlaceholder);
            }
        }
    }

    // Create friend list placeholder
    createFriendListPlaceholder() {
        const container = document.createElement('div');
        container.className = 'friend-list-placeholder';

        // Add friend item placeholders
        for (let i = 0; i < this.config.friendItems; i++) {
            container.appendChild(this.createFriendItemPlaceholder());
        }

        // Add group chat placeholders
        for (let i = 0; i < this.config.groupChats; i++) {
            container.appendChild(this.createGroupChatPlaceholder());
        }

        return container;
    }

    // Create friend item placeholder
    createFriendItemPlaceholder() {
        const item = document.createElement('div');
        item.className = 'friend-item-placeholder';

        const avatar = document.createElement('div');
        avatar.className = 'friend-avatar-placeholder';

        const info = document.createElement('div');
        info.className = 'friend-info-placeholder';

        const name = document.createElement('div');
        name.className = 'friend-name-placeholder';

        const status = document.createElement('div');
        status.className = 'friend-status-placeholder';

        info.appendChild(name);
        info.appendChild(status);

        item.appendChild(avatar);
        item.appendChild(info);

        return item;
    }

    // Create group chat placeholder
    createGroupChatPlaceholder() {
        const item = document.createElement('div');
        item.className = 'group-chat-placeholder';

        const avatar = document.createElement('div');
        avatar.className = 'group-avatar-placeholder';

        const info = document.createElement('div');
        info.className = 'group-info-placeholder';

        const name = document.createElement('div');
        name.className = 'group-name-placeholder';

        const members = document.createElement('div');
        members.className = 'group-members-placeholder';

        info.appendChild(name);
        info.appendChild(members);

        item.appendChild(avatar);
        item.appendChild(info);

        return item;
    }

    // Create chat placeholder
    createChatPlaceholder() {
        const container = document.createElement('div');
        container.className = 'chat-placeholder';

        // If empty state is enabled, show that instead of messages
        if (this.config.emptyState) {
            container.appendChild(this.createEmptyStatePlaceholder());
            return container;
        }

        // Add message placeholders
        for (let i = 0; i < this.config.messages; i++) {
            // Alternate between incoming and outgoing messages
            const isOutgoing = i % 2 === 0;
            container.appendChild(this.createMessagePlaceholder(isOutgoing));
        }

        return container;
    }

    // Create empty state placeholder
    createEmptyStatePlaceholder() {
        const container = document.createElement('div');
        container.className = 'empty-state-placeholder';

        const icon = document.createElement('div');
        icon.className = 'empty-icon-placeholder';

        const title = document.createElement('div');
        title.className = 'empty-title-placeholder';

        const text1 = document.createElement('div');
        text1.className = 'empty-text-placeholder';

        const text2 = document.createElement('div');
        text2.className = 'empty-text-placeholder';

        container.appendChild(icon);
        container.appendChild(title);
        container.appendChild(text1);
        container.appendChild(text2);

        return container;
    }

    // Create message placeholder
    createMessagePlaceholder(isOutgoing = false) {
        const message = document.createElement('div');
        message.className = `message-placeholder ${isOutgoing ? 'outgoing' : 'incoming'}`;

        const avatar = document.createElement('div');
        avatar.className = 'message-avatar-placeholder';

        const content = document.createElement('div');
        content.className = 'message-content-placeholder';

        // Add 2-3 lines of text
        const lines = Math.floor(Math.random() * 2) + 2;
        for (let i = 0; i < lines; i++) {
            const text = document.createElement('div');
            text.className = 'message-text-placeholder';
            content.appendChild(text);
        }

        message.appendChild(avatar);
        message.appendChild(content);

        return message;
    }

    // Manually trigger loading state
    startLoading() {
        this.showLoading();
    }

    // Manually end loading state
    endLoading() {
        this.hideLoading();
    }
}

// Initialize the loading manager when the DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Create global instance
    window.friendsLoadingManager = new FriendsLoadingManager();

    // Auto-show loading on page load if friend list is empty
    const friendList = document.querySelector('.friend-list') || document.getElementById('friendList');
    if (friendList && (!friendList.children.length || friendList.children.length === 0)) {
        window.friendsLoadingManager.showLoading();
    }
});

// Expose API for manual control
window.showFriendsLoading = () => {
    if (window.friendsLoadingManager) {
        window.friendsLoadingManager.showLoading();
    }
};

window.hideFriendsLoading = (type) => {
    if (window.friendsLoadingManager) {
        if (type === 'friends') {
            window.friendsLoadingManager.friendsLoaded = true;
        } else if (type === 'group_chats') {
            window.friendsLoadingManager.groupChatsLoaded = true;
        } else {
            // If no type specified, force hide loading
            window.friendsLoadingManager.hideLoading();
            return;
        }

        // Check if we can hide the loading placeholders
        window.friendsLoadingManager.checkAndHideLoading();
    }
};
