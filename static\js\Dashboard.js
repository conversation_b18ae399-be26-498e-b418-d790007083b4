/**
 * Dashboard.js
 * Main dashboard functionality and initialization
 */
class Dashboard {
    constructor() {
        this.serviceManager = new ServiceManager();
        // Get the last view from localStorage or default to 'dashboard'
        this.currentView = localStorage.getItem('lastDashboardView') || 'dashboard';
        this.views = ['dashboard', 'store', 'profile', 'friends', 'settings', 'contact', 'statistics'];
        this.isDarkMode = true;
        this.showAllUpdates = false;
        this.updates = [];
        this.storeServices = [];
        this.storeFilter = { search: '', category: 'all' };
        this.contacts = [];
        this.isAdminMode = false; // Default to user mode
        this.isUserAdmin = false; // Default to non-admin user
        this.isUserSuperAdmin = false; // Default to non-super-admin user
    }

    /**
     * Clean up test services from localStorage
     */
    cleanupTestServices() {
        console.log('Cleaning up test services from localStorage...');
        const storedConfigs = localStorage.getItem('serviceConfigurations');

        if (storedConfigs) {
            try {
                const configs = JSON.parse(storedConfigs);

                // Filter out test services
                const cleanedConfigs = configs.filter(service =>
                    service.name !== 'asd' &&
                    service.description !== 'asd' &&
                    service.name !== 'sda' &&
                    service.description !== 'sda' &&
                    service.id !== 'asd' &&
                    service.id !== 'sda'
                );

                // Save cleaned configurations back to localStorage
                localStorage.setItem('serviceConfigurations', JSON.stringify(cleanedConfigs));
                console.log('Cleaned service configurations saved to localStorage');

                // Log removed services
                const removedCount = configs.length - cleanedConfigs.length;
                if (removedCount > 0) {
                    console.log(`Removed ${removedCount} test service(s)`);
                }
            } catch (e) {
                console.warn('Failed to parse stored service configurations:', e);
            }
        }
    }

    /**
     * Remove duplicate services from the store
     */
    removeDuplicateStoreServices() {
        console.log('Removing duplicate services from store...');

        // Use a Map to track services by ID
        const uniqueServices = new Map();

        // Process services and keep only the latest version of each
        this.storeServices.forEach(service => {
            uniqueServices.set(service.id, service);
        });

        // Convert back to array
        this.storeServices = Array.from(uniqueServices.values());

        console.log(`Store now has ${this.storeServices.length} unique services`);
    }

    /**
     * Initialize the dashboard
     */
    async init() {
        console.log('Initializing Dashboard...');

        // Clean up test services
        this.cleanupTestServices();

        // Initialize Lucide icons
        if (window.lucide) {
            lucide.createIcons();
        }

        // Switch to the last saved view (or default if none saved)
        console.log(`Restoring last view: ${this.currentView}`);
        this.switchView(this.currentView);

        // Initialize service manager
        console.log('Initializing ServiceManager...');
        await this.serviceManager.init();
        console.log('ServiceManager initialized');

        // Set up service manager event listeners
        this.setupServiceManagerEvents();

        // Set up view switching
        this.setupViewSwitching();

        // Set up theme toggle
        this.setupThemeToggle();

        // Set up user menu
        this.setupUserMenu();

        // Set up admin mode toggle
        this.setupAdminModeToggle();

        // Set up updates section
        await this.loadUpdates();
        this.setupUpdatesToggle();

        // Set up store section
        this.loadStore();
        this.setupStoreFilters();

        // Update time display
        this.setupTimeDisplay();

        // Set up button event handlers
        this.setupButtonHandlers();

        // Check admin status first before setting up admin-related features
        this.checkAdminStatus().then(() => {
            // Only set up admin management if user is an admin
            if (this.isUserAdmin) {
                // Set up admin management
                this.setupAdminManagement();
            }

            // Load admin contacts for contact page (available to all users)
            this.loadContacts();
        });

        // Set up user management
        this.setupUserManagement();


        // Set up service restrictions
        this.setupServiceRestrictions();

        // Set up settings
        this.setupSettings();

        // Set up responsive handling
        this.setupResponsiveHandling();

        // Set up global search
        this.setupGlobalSearch();

        console.log('Dashboard initialization complete');
    }

    /**
     * Set up view switching functionality
     */
    setupViewSwitching() {
        // Add click handlers to navigation items
        document.querySelectorAll('[data-view]').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation(); // Stop event propagation to prevent document click handler

                // Get the view to switch to
                const viewName = e.currentTarget.dataset.view;
                this.switchView(viewName);

                // Close the mobile sidebar if it's open
                const isMobile = window.innerWidth <= 768;
                if (isMobile) {
                    const sidebar = document.querySelector('.sidebar-container');

                    if (sidebar && sidebar.classList.contains('open')) {
                        sidebar.classList.remove('open');
                    }
                }

                console.log(`Switched to view: ${viewName} via navigation click`);
            });
        });
    }

    /**
     * Switch to a different view
     * @param {string} viewName Name of the view to switch to
     */
    switchView(viewName) {
        if (!this.views.includes(viewName)) {
            console.error(`View ${viewName} not found`);
            return;
        }

        // Store previous view for reference
        const previousView = this.currentView;

        // Update current view
        this.currentView = viewName;

        // Save current view to localStorage
        localStorage.setItem('lastDashboardView', viewName);

        // Hide all views
        this.views.forEach(view => {
            const viewElement = document.getElementById(`${view}View`);
            const navItems = document.querySelectorAll(`[data-view="${view}"]`);

            if (viewElement) {
                if (view === viewName) {
                    viewElement.classList.remove('hidden');
                } else {
                    viewElement.classList.add('hidden');
                }
            }

            // Update all navigation items with this view (could be multiple)
            navItems.forEach(navItem => {
                if (view === viewName) {
                    navItem.classList.add('active');
                } else {
                    navItem.classList.remove('active');
                }
            });
        });

        // If switching to store view, update the store info message visibility
        if (viewName === 'store') {
            this.updateStoreInfoMessage();
        }

        // If switching to dashboard view from another view, refresh updates
        if (viewName === 'dashboard' && previousView !== 'dashboard') {
            console.log('Returning to dashboard view, refreshing updates...');
            this.loadUpdates();
        }

        // If switching to settings view, always reset the settings tabs
        if (viewName === 'settings') {
            // Force reset settings tabs to ensure consistent state
            console.log('Switching to settings view, resetting tabs');
            this.resetSettingsTabs();

            // Also fix the settings sections visibility
            this.fixSettingsSections();
        }

        // Dispatch a custom event for the view change that other components can listen for
        document.dispatchEvent(new CustomEvent('view-changed', {
            detail: {
                view: viewName,
                previousView: previousView
            }
        }));

        // If switching to statistics view, initialize the statistics panel
        if (viewName === 'statistics') {
            console.log('Switching to statistics view, initializing panel');
        }

        // Log the view change for debugging
        console.log(`Switched to view: ${viewName} from ${previousView}`);
    }

    /**
     * Set up theme toggle functionality
     */
    setupThemeToggle() {
        const themeToggle = document.getElementById('themeToggle');
        const darkModeToggle = document.getElementById('darkModeToggle');

        if (themeToggle) {
            themeToggle.addEventListener('click', () => this.toggleTheme());
        }

        if (darkModeToggle) {
            darkModeToggle.addEventListener('click', () => this.toggleTheme());
        }
    }

    /**
     * Toggle between light and dark theme
     */
    toggleTheme() {
        this.isDarkMode = !this.isDarkMode;
        document.body.classList.toggle('dark', this.isDarkMode);

        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.innerHTML = this.isDarkMode ?
                '<i data-lucide="moon" class="h-5 w-5"></i>' :
                '<i data-lucide="sun" class="h-5 w-5"></i>';
        }

        // Update dark mode toggle in settings
        const darkModeToggle = document.getElementById('darkModeToggle');
        if (darkModeToggle) {
            const thumb = darkModeToggle.querySelector('span');
            if (thumb) {
                if (this.isDarkMode) {
                    thumb.classList.add('toggle-thumb', 'active');
                } else {
                    thumb.classList.remove('active');
                }
            }
            darkModeToggle.classList.toggle('bg-cyan-500', this.isDarkMode);
        }

        // Refresh icons
        if (window.lucide) {
            lucide.createIcons();
        }
    }

    /**
     * Set up user menu functionality
     */
    setupUserMenu() {
        const userMenuTrigger = document.getElementById('userMenuTrigger');
        const userMenu = document.getElementById('userMenu');

        if (userMenuTrigger && userMenu) {
            // Fetch current user data
            this.fetchCurrentUser();

            userMenuTrigger.addEventListener('click', () => {
                // If menu is hidden, prepare it for animation before showing
                if (userMenu.classList.contains('hidden')) {
                    // Set initial state for animation
                    userMenu.style.opacity = '0';
                    userMenu.style.transform = 'scale(0.95)';
                    userMenu.classList.remove('hidden');

                    // Force a reflow to ensure the initial state is applied
                    void userMenu.offsetWidth;

                    // Apply the animated state
                    userMenu.style.opacity = '1';
                    userMenu.style.transform = 'scale(1)';
                } else {
                    // Animate out
                    userMenu.style.opacity = '0';
                    userMenu.style.transform = 'scale(0.95)';

                    // Hide after animation completes
                    setTimeout(() => {
                        userMenu.classList.add('hidden');
                        // Reset styles for next opening
                        userMenu.style.opacity = '';
                        userMenu.style.transform = '';
                    }, 200); // Match the transition duration
                }
            });

            // Close user menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!userMenuTrigger.contains(e.target) && !userMenu.contains(e.target) && !userMenu.classList.contains('hidden')) {
                    // Animate out
                    userMenu.style.opacity = '0';
                    userMenu.style.transform = 'scale(0.95)';

                    // Hide after animation completes
                    setTimeout(() => {
                        userMenu.classList.add('hidden');
                        // Reset styles for next opening
                        userMenu.style.opacity = '';
                        userMenu.style.transform = '';
                    }, 200); // Match the transition duration
                }
            });

            // Set up profile link in user menu
            const profileLink = userMenu.querySelector('#profileLink');
            if (profileLink) {
                profileLink.addEventListener('click', () => {
                    // Animate out
                    userMenu.style.opacity = '0';
                    userMenu.style.transform = 'scale(0.95)';

                    // Hide after animation completes and switch view
                    setTimeout(() => {
                        userMenu.classList.add('hidden');
                        // Reset styles for next opening
                        userMenu.style.opacity = '';
                        userMenu.style.transform = '';
                        this.switchView('profile');
                    }, 200); // Match the transition duration
                });
            }

            // Set up sign out button
            const signOutBtn = userMenu.querySelector('a[href="/auth/logout"]');
            if (signOutBtn) {
                signOutBtn.addEventListener('click', () => {
                    window.location.href = '/auth/logout';
                });
            }
        }
    }

    /**
     * Fetch current user data from the server
     */
    fetchCurrentUser() {
        fetch('/auth/current-user')
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    console.error('Error fetching user data:', data.error);
                    return;
                }

                // Update user menu with actual data
                this.updateUserMenuWithData(data);
            })
            .catch(error => {
                console.error('Error fetching user data:', error);
            });
    }

    /**
     * Update user menu with actual user data
     * @param {Object} userData - User data from the server
     */
    updateUserMenuWithData(userData) {
        // Update user initials or profile picture in the menu trigger
        const userMenuTrigger = document.getElementById('userMenuTrigger');
        if (userMenuTrigger) {
            const initialsContainer = userMenuTrigger.querySelector('div');
            if (initialsContainer) {
                // Check if user has a profile picture (from Google)
                if (userData.profile_picture) {
                    // Clear the text content
                    initialsContainer.textContent = '';

                    // Create or update the profile image
                    let profileImg = initialsContainer.querySelector('img');
                    if (!profileImg) {
                        profileImg = document.createElement('img');
                        profileImg.className = 'w-full h-full rounded-full object-cover';
                        initialsContainer.appendChild(profileImg);
                    }

                    // Set the profile image source
                    profileImg.src = userData.profile_picture;

                    // Remove background colors
                    initialsContainer.classList.remove('bg-slate-700', 'bg-red-900');
                    ['bg-cyan-600', 'bg-purple-600', 'bg-green-600', 'bg-amber-600', 'bg-red-600', 'bg-blue-600', 'bg-pink-600']
                        .forEach(color => initialsContainer.classList.remove(color));
                } else {
                    // Set user initials
                    initialsContainer.textContent = userData.initials || 'KS';

                    // Remove any existing profile image
                    const profileImg = initialsContainer.querySelector('img');
                    if (profileImg) {
                        profileImg.remove();
                    }

                    // Set a random background color based on the username
                    const colors = ['bg-cyan-600', 'bg-purple-600', 'bg-green-600', 'bg-amber-600', 'bg-red-600', 'bg-blue-600', 'bg-pink-600'];
                    const colorIndex = userData.username ? userData.username.charCodeAt(0) % colors.length : 0;

                    // Remove existing background color
                    initialsContainer.classList.remove('bg-slate-700', 'bg-red-900');
                    colors.forEach(color => initialsContainer.classList.remove(color));

                    // Add new background color
                    initialsContainer.classList.add(colors[colorIndex]);
                }
            }
        }

        // Update user info in the menu
        const userMenu = document.getElementById('userMenu');
        if (userMenu) {
            const userName = userMenu.querySelector('.text-sm.font-medium');
            const userEmail = userMenu.querySelector('.text-xs.text-slate-400');

            if (userName && userData.username) {
                userName.textContent = userData.username;
            }

            if (userEmail && userData.email) {
                userEmail.textContent = userData.email;
            }
        }
    }

    /**
     * Load updates data
     */
    async loadUpdates() {
        // Show loading state
        const updatesContainer = document.getElementById('updatesContainer');
        if (updatesContainer) {
            updatesContainer.innerHTML = `
                <div class="col-span-full flex justify-center items-center py-6">
                    <div class="flex items-center">
                        <i data-lucide="loader" class="h-5 w-5 text-slate-500 animate-spin mr-2"></i>
                        <span class="text-slate-500 text-sm">Loading updates...</span>
                    </div>
                </div>
            `;

            // Refresh icons
            if (window.lucide) {
                lucide.createIcons();
            }
        }

        // Get updates from ServiceConfig
        if (window.ServiceConfig && typeof window.ServiceConfig.getAllUpdates === 'function') {
            console.log('Loading updates from ServiceConfig...');

            // Get the list of services the user has added to their dashboard
            const userDashboardServices = Array.from(this.serviceManager.services.keys());
            console.log('User dashboard services:', userDashboardServices);

            // If no services are added to the dashboard yet, show no updates
            if (userDashboardServices.length === 0) {
                console.log('No services in user dashboard, showing no updates');
                this.updates = [];
                return this.updateUpdatesDisplay();
            }

            try {
                // Make sure service IDs are properly formatted for the API
                // This ensures consistent casing for service IDs like 'kevkoAI'
                const formattedServiceIds = userDashboardServices.map(id => {
                    // Find the service in the config to get the correct casing
                    const configService = window.ServiceConfig.services.find(s =>
                        s.id.toLowerCase() === id.toLowerCase()
                    );
                    return configService ? configService.id : id;
                });

                console.log('Formatted service IDs for updates:', formattedServiceIds);

                // Get updates only from services that the user has added to their dashboard
                this.updates = await window.ServiceConfig.getAllUpdates(formattedServiceIds);
                console.log(`Loaded ${this.updates.length} updates from user's dashboard services`);
            } catch (error) {
                console.error('Error loading updates:', error);
                this.updates = [];
            }
        } else {
            console.warn('ServiceConfig.getAllUpdates not available, using empty updates');
            this.updates = [];
        }

        // Display updates
        this.updateUpdatesDisplay();
    }

    /**
     * Set up service manager event listeners
     */
    setupServiceManagerEvents() {
        // Listen for service added event
        this.serviceManager.on('serviceAdded', async () => {
            console.log('Service added, refreshing updates...');
            await this.loadUpdates();
        });

        // Listen for service removed event
        this.serviceManager.on('serviceRemoved', async () => {
            console.log('Service removed, refreshing updates...');
            await this.loadUpdates();
        });
    }

    /**
     * Set up updates toggle functionality
     */
    setupUpdatesToggle() {
        const toggleButton = document.getElementById('toggleUpdates');

        if (toggleButton) {
            toggleButton.addEventListener('click', () => {
                this.showAllUpdates = !this.showAllUpdates;
                this.updateUpdatesDisplay();
            });
        }
    }

    /**
     * Update the updates display
     */
    updateUpdatesDisplay() {
        const updatesContainer = document.getElementById('updatesContainer');
        const toggleText = document.getElementById('toggleText');
        const toggleIcon = document.getElementById('toggleIcon');
        const toggleButton = document.getElementById('toggleUpdates');

        if (!updatesContainer) return;

        // Check if there are any updates to display
        if (this.updates.length === 0) {
            // Display a message when there are no updates
            updatesContainer.innerHTML = `
                <div class="col-span-full text-center py-6">
                    <div class="flex flex-col items-center justify-center">
                        <i data-lucide="info" class="h-8 w-8 text-slate-500 mb-2"></i>
                        <p class="text-slate-400">No updates available for your dashboard services.</p>
                        <p class="text-slate-500 text-sm mt-1">Add more services from the Store to see their updates.</p>
                    </div>
                </div>
            `;

            // Hide the toggle button when there are no updates
            if (toggleButton) {
                toggleButton.classList.add('hidden');
            }
        } else {
            // Show the toggle button when there are updates
            if (toggleButton) {
                toggleButton.classList.remove('hidden');
            }

            // Display the updates
            const displayUpdates = this.showAllUpdates ? this.updates : this.updates.slice(0, 4);
            updatesContainer.innerHTML = displayUpdates.map(update => this.createUpdateCard(update)).join('');

            // Update toggle button text
            if (toggleText) {
                toggleText.textContent = this.showAllUpdates ? "Show Less" : "Show All";
            }
        }

        // Refresh icons
        if (window.lucide) {
            lucide.createIcons();
        }

        if (toggleIcon) {
            toggleIcon.setAttribute('data-lucide', this.showAllUpdates ? 'chevron-up' : 'chevron-down');
            // Refresh toggle icon
            if (window.lucide) {
                lucide.createIcons();
            }
        }
    }

    /**
     * Create an update card HTML
     * @param {Object} update Update data
     * @returns {string} HTML string
     */
    createUpdateCard(update) {
        const colorClasses = {
            amber: "text-amber-500 bg-amber-500/10 border-amber-500/20",
            cyan: "text-cyan-500 bg-cyan-500/10 border-cyan-500/20",
            blue: "text-blue-500 bg-blue-500/10 border-blue-500/20",
            green: "text-green-500 bg-green-500/10 border-green-500/20",
            purple: "text-purple-500 bg-purple-500/10 border-purple-500/20",
            pink: "text-pink-500 bg-pink-500/10 border-pink-500/20",
            indigo: "text-indigo-500 bg-indigo-500/10 border-indigo-500/20",
            violet: "text-violet-500 bg-violet-500/10 border-violet-500/20",
            slate: "text-slate-400 bg-slate-500/10 border-slate-500/20"
        };

        // Default to slate if color not found
        const colorClass = colorClasses[update.color] || colorClasses.slate;

        // Add version badge if available
        const versionBadge = update.version ?
            `<span class="text-xs px-2 py-0.5 rounded-full bg-slate-700/50 border border-slate-600/30 ml-2">${update.version}</span>` : '';

        return `
            <div class="update-item">
                <div class="icon-container ${colorClass}">
                    <i data-lucide="${update.icon}" class="h-5 w-5"></i>
                </div>
                <div class="flex-1">
                    <div class="flex items-center justify-between mb-1">
                        <div class="flex items-center">
                            <span class="text-sm font-medium text-slate-300">${update.service}</span>
                            ${versionBadge}
                        </div>
                        <span class="text-xs text-slate-500">${update.date}</span>
                    </div>
                    <h4 class="text-sm font-medium text-slate-200">${update.title}</h4>
                    <p class="text-xs text-slate-400">${update.description}</p>
                </div>
            </div>
        `;
    }

    /**
     * Set up time display
     */
    setupTimeDisplay() {
        // Update time initially
        this.updateTime();

        // Update time every second
        setInterval(() => this.updateTime(), 1000);
    }

    /**
     * Set up general button event handlers
     */
    setupButtonHandlers() {
        console.log('Setting up button event handlers');

        // Add event handlers for general buttons that aren't covered by other setup methods
        // For example, buttons that don't belong to specific sections like store, updates, etc.

        // Example: Setup refresh button if it exists
        const refreshBtn = document.getElementById('refreshDashboard');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                console.log('Refreshing dashboard data...');
                this.loadUpdates();
                this.loadStore();
                // Add other refresh actions as needed
            });
        }

        // Add other general button handlers here
    }

    /**
     * Update the time display
     */
    updateTime() {
        const now = new Date();
        const timeElement = document.getElementById('currentTime');
        const dateElement = document.getElementById('currentDate');

        if (timeElement && dateElement) {
            timeElement.textContent = now.toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit'
            });
            dateElement.textContent = now.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }
    }



    /**
     * Load store data
     */
    loadStore() {
        // Get available services from ServiceConfig
        if (window.ServiceConfig && typeof window.ServiceConfig.getAvailableServices === 'function') {
            console.log('Loading store services from ServiceConfig...');
            this.storeServices = window.ServiceConfig.getAvailableServices();

            // Remove any duplicate services
            this.removeDuplicateStoreServices();
        } else {
            console.warn('ServiceConfig.getAvailableServices not available, using default services');
            // Fallback to default services
            this.storeServices = [
                {
                    id: 'kevkoHome',
                    name: 'KevkoHome',
                    description: 'Smart home management',
                    icon: 'home',
                    color: 'amber',
                    category: 'Smart Home',
                    version: '1.0.0',
                    author: 'Kevko',
                    features: [
                        'Device control',
                        'Room management',
                        'Energy monitoring',
                        'Automation'
                    ],
                    longDescription: 'KevkoHome is a comprehensive smart home management system that allows you to control all your connected devices from a single interface. Monitor energy usage, create automation rules, and manage rooms and devices with ease. The system supports a wide range of smart home devices including lights, thermostats, locks, cameras, and more.',
                    requirements: [
                        'Compatible smart home devices',
                        'Local network connection',
                        'Internet connection for remote access'
                    ],
                    screenshots: [
                        '/static/img/services/kevkoHome/screenshot1.jpg',
                        '/static/img/services/kevkoHome/screenshot2.jpg'
                    ]
                },

                {
                    id: 'kevkoAI',
                    name: 'KevkoAI',
                    description: 'AI chat with multiple models',
                    icon: 'bot',
                    color: 'blue',
                    category: 'AI',
                    version: '1.0.0',
                    author: 'Kevko',
                    features: [
                        'AI chat',
                        'Multiple models',
                        'Conversation history',
                        'File uploads'
                    ],
                    longDescription: 'KevkoAI is an advanced AI chat service that allows you to interact with multiple AI models. Engage in conversations, ask questions, and receive responses from various AI models. The service supports file uploads, enabling you to provide context or additional information for more accurate responses. Enhance your AI experience with KevkoAI.',
                    requirements: [
                        'Internet connection',
                        'KevkoFy account (free or premium)'
                    ],
                    screenshots: [
                        '/static/img/services/kevkoFy/screenshot1.jpg',
                        '/static/img/services/kevkoFy/screenshot2.jpg'
                    ]
                },
                {
                    id: 'kevkoFriends',
                    name: 'Friends',
                    description: 'Chat with friends and invite them to live sessions',
                    icon: 'users-2',
                    color: 'violet',
                    category: 'Social',
                    version: '1.0.0',
                    author: 'Kevko',
                    features: [
                        'Chat with friends',
                        'Friend management',
                        'Invite friends to live sessions',
                        'Real-time messaging'
                    ],
                    longDescription: 'The Friends service allows you to connect with other users on the platform. Add friends by username, chat with them in real-time, and invite them to join your live sessions. The service includes individual chat pages for private conversations and group chat functionality for collaborative discussions. Stay connected with your network and enhance your collaborative experience.',
                    requirements: [
                        'Active account',
                        'Internet connection'
                    ],
                    screenshots: [
                        '/static/img/services/kevkoFriends/screenshot1.jpg',
                        '/static/img/services/kevkoFriends/screenshot2.jpg'
                    ]
                }
            ];
        }

        console.log(`Loaded ${this.storeServices.length} store services`);

        // Display store services
        this.updateStoreDisplay();
    }

    /**
     * Set up store filters
     */
    setupStoreFilters() {
        const searchInput = document.getElementById('storeSearch');
        const categorySelect = document.getElementById('storeCategory');

        if (searchInput) {
            searchInput.addEventListener('input', () => {
                this.storeFilter.search = searchInput.value.toLowerCase();
                this.updateStoreDisplay();
            });
        }

        if (categorySelect) {
            categorySelect.addEventListener('change', () => {
                this.storeFilter.category = categorySelect.value;
                this.updateStoreDisplay();
            });
        }
    }

    /**
     * Check if any services are installed and update the store info message visibility
     */
    updateStoreInfoMessage() {
        const storeInfoMessage = document.getElementById('storeInfoMessage');
        if (!storeInfoMessage) {
            return;
        }

        // Check if any services are installed
        const hasInstalledServices = this.serviceManager.services.size > 0;

        // Show or hide the info message based on whether services are installed
        if (hasInstalledServices) {
            storeInfoMessage.classList.add('hidden');
        } else {
            storeInfoMessage.classList.remove('hidden');
        }

        console.log(`Store info message visibility updated. Has installed services: ${hasInstalledServices}`);
    }

    /**
     * Update the store display
     */
    updateStoreDisplay() {
        const storeContainer = document.getElementById('storeContainer');

        if (!storeContainer) {
            console.error('Store container not found');
            return;
        }

        // Update the store info message visibility
        this.updateStoreInfoMessage();

        // Filter services
        const filteredServices = this.storeServices.filter(service => {
            // Filter by search
            const matchesSearch = this.storeFilter.search === '' ||
                service.name.toLowerCase().includes(this.storeFilter.search) ||
                service.description.toLowerCase().includes(this.storeFilter.search);

            // Filter by category
            const matchesCategory = this.storeFilter.category === 'all' ||
                service.category === this.storeFilter.category;

            return matchesSearch && matchesCategory;
        });

        // Clear container
        storeContainer.innerHTML = '';

        // Add store items
        if (filteredServices.length === 0) {
            storeContainer.innerHTML = `
                <div class="col-span-full text-center py-8">
                    <p class="text-slate-400">No services found matching your criteria.</p>
                </div>
            `;
            return;
        }

        // Add each service
        for (const service of filteredServices) {
            const isInstalled = this.serviceManager.services.has(service.id);
            const serviceCard = document.createElement('div');
            const cardBorder = isInstalled ? `border-${service.color}-500/30` : 'border-slate-700/50';
            serviceCard.className = `bg-slate-800/50 border ${cardBorder} rounded-lg overflow-hidden cursor-pointer hover:bg-slate-800/80 transition-colors`;
            serviceCard.setAttribute('data-service-id', service.id);
            serviceCard.innerHTML = `
                <div class="p-6 relative">
                    <div class="flex items-start mb-4">
                        <div class="p-2 rounded-lg bg-${service.color}-500/10 mr-3">
                            <i data-lucide="${service.icon}" class="h-6 w-6 text-${service.color}-500"></i>
                        </div>
                        <div class="flex-grow">
                            <div class="flex justify-between">
                                <span class="text-xs text-slate-400 bg-slate-700/50 px-2 py-1 rounded">${service.category}</span>
                                <!-- Rest of the card content -->
                            </div>
                        </div>
                    </div>
                    <h3 class="text-lg font-medium text-slate-100 mb-1">${service.name}</h3>
                    <div class="flex items-center text-xs text-slate-400 mb-2">
                        <span>v${service.version}</span>
                        <span class="mx-1">•</span>
                        <span>By ${service.author}</span>
                    </div>
                    <p class="text-sm text-slate-400 mb-4">${service.description}</p>
                    <div class="mb-4">
                        <h4 class="text-xs font-medium text-slate-300 mb-2">Features</h4>
                        <ul class="text-xs text-slate-400 space-y-1 pl-4">
                            ${service.features.map(feature => `<li class="list-disc list-inside">${feature}</li>`).join('')}
                        </ul>
                    </div>
                    ${isInstalled ? `
                    <button class="store-remove-btn w-full bg-slate-700/50 hover:bg-red-900/50 text-slate-300 hover:text-red-200 rounded-md px-4 py-3 text-lg font-medium flex items-center justify-center mt-6" data-service-id="${service.id}">
                        <i data-lucide="trash-2" class="h-5 w-5 mr-2"></i>
                        Remove from Dashboard
                    </button>
                    ` : `
                    <button class="store-install-btn w-full bg-cyan-600 hover:bg-cyan-500 text-white rounded-md px-4 py-3 text-lg font-medium flex items-center justify-center mt-6" data-service-id="${service.id}">
                        <i data-lucide="plus-circle" class="h-5 w-5 mr-2"></i>
                        Add to Dashboard
                    </button>
                    `}
                </div>
            `;

            storeContainer.appendChild(serviceCard);
        }

        // Initialize Lucide icons
        if (window.lucide) {
            lucide.createIcons();
        }

        // Add event listeners to install buttons
        document.querySelectorAll('.store-install-btn:not([disabled])').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation(); // Prevent card click event
                const serviceId = btn.getAttribute('data-service-id');
                this.installService(serviceId);
            });
        });

        // Add event listeners to remove buttons
        document.querySelectorAll('.store-remove-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation(); // Prevent card click event
                const serviceId = btn.getAttribute('data-service-id');
                this.removeService(serviceId);
            });
        });

        // Add event listeners to service cards for showing details
        document.querySelectorAll('#storeContainer > div').forEach(card => {
            card.addEventListener('click', () => {
                const serviceId = card.getAttribute('data-service-id');
                if (serviceId) {
                    this.showServiceDetails(serviceId);
                }
            });
        });
    }

    /**
     * Install a service from the store
     * @param {string} serviceId Service ID
     */
    async installService(serviceId) {
        console.log(`Installing service: ${serviceId}`);

        try {
            // Find service in store
            const storeService = this.storeServices.find(s => s.id === serviceId);
            if (!storeService) {
                console.error(`Service ${serviceId} not found in store`);
                return;
            }

            // Check if service is already installed
            if (this.serviceManager.services.has(serviceId)) {
                console.warn(`Service ${serviceId} is already installed`);
                return;
            }

            // Define routes for specific services
            const serviceRoutes = {
                'kevkoAI': '/chat',
                'kevkoFy': '/spotify'
            };

            // Create service config
            const config = {
                id: storeService.id,
                name: storeService.name,
                description: storeService.description,
                icon: storeService.icon,
                color: storeService.color,
                status: 'online',
                url: serviceRoutes[storeService.id] || null,
                type: 'internal',
                className: storeService.id.charAt(0).toUpperCase() + storeService.id.slice(1) + 'Service'
            };

            // Register service - this will make it visible on the dashboard
            console.log('Registering service with config:', JSON.stringify(config, null, 2));
            const registeredService = await this.serviceManager.registerService(config);
            console.log('Registered service:', registeredService ? registeredService.id : 'failed',
                        'URL:', registeredService ? registeredService.url : 'N/A');

            if (!registeredService) {
                throw new Error('Service registration failed');
            }

            // Explicitly save service configurations to ensure they persist
            this.serviceManager.saveServiceConfigurations();
            console.log('Service configurations saved after adding service');

            // Update store display
            this.updateStoreDisplay();

            // Update the store info message visibility
            this.updateStoreInfoMessage();

            // Switch to dashboard view to show the newly added service
            this.switchView('dashboard');

        } catch (error) {
            console.error(`Failed to install service ${serviceId}:`, error);
            alert(`Failed to install service: ${error.message}`);
        }
    }

    /**
     * Show service details modal
     * @param {string} serviceId Service ID
     */
    showServiceDetails(serviceId) {
        console.log(`Showing details for service: ${serviceId}`);

        // Find service in store
        const service = this.storeServices.find(s => s.id === serviceId);
        if (!service) {
            console.error(`Service ${serviceId} not found in store`);
            return;
        }

        // Get modal elements
        const modal = document.getElementById('serviceDetailsModal');
        const title = document.getElementById('serviceDetailsTitle');
        const icon = document.getElementById('serviceDetailsIcon');
        const content = document.getElementById('serviceDetailsContent');
        const actionButtons = document.getElementById('serviceDetailsActionButtons');
        const closeButton = document.getElementById('closeServiceDetailsModal');
        const backButton = document.getElementById('serviceDetailsBackButton');

        if (!modal || !title || !icon || !content || !actionButtons || !closeButton || !backButton) {
            console.error('Service details modal elements not found');
            return;
        }

        // Set title and icon
        title.textContent = service.name;
        icon.className = `p-2 rounded-lg bg-${service.color}-500/10 mr-3`;
        icon.innerHTML = `<i data-lucide="${service.icon}" class="h-6 w-6 text-${service.color}-500"></i>`;

        // Check if service is installed
        const isInstalled = this.serviceManager.services.has(serviceId);

        // Set content
        content.innerHTML = `
            <div class="space-y-4">
                <div class="flex items-center">
                    <span class="text-xs text-slate-400 bg-slate-700/50 px-2 py-1 rounded">${service.category}</span>
                    ${isInstalled ? `<span class="text-xs bg-${service.color}-500/20 text-${service.color}-400 px-2 py-1 rounded-full ml-2">Installed</span>` : ''}
                    <div class="flex-grow"></div>
                    <div class="text-xs text-slate-400">
                        <span>v${service.version}</span>
                        <span class="mx-1">•</span>
                        <span>By ${service.author}</span>
                    </div>
                </div>

                <div class="bg-slate-700/30 p-4 rounded-lg">
                    <h4 class="text-sm font-medium text-slate-200 mb-2">Description</h4>
                    <p class="text-sm text-slate-300">${service.description}</p>
                </div>

                <div class="bg-slate-700/30 p-4 rounded-lg">
                    <h4 class="text-sm font-medium text-slate-200 mb-2">Features</h4>
                    <ul class="text-sm text-slate-300 space-y-2 pl-4">
                        ${service.features.map(feature => `<li class="list-disc list-inside">${feature}</li>`).join('')}
                    </ul>
                </div>

                ${service.longDescription ? `
                <div class="bg-slate-700/30 p-4 rounded-lg">
                    <h4 class="text-sm font-medium text-slate-200 mb-2">Detailed Description</h4>
                    <p class="text-sm text-slate-300">${service.longDescription}</p>
                </div>
                ` : ''}

                ${service.requirements ? `
                <div class="bg-slate-700/30 p-4 rounded-lg">
                    <h4 class="text-sm font-medium text-slate-200 mb-2">Requirements</h4>
                    <ul class="text-sm text-slate-300 space-y-2 pl-4">
                        ${service.requirements.map(req => `<li class="list-disc list-inside">${req}</li>`).join('')}
                    </ul>
                </div>
                ` : ''}

                ${service.screenshots ? `
                <div class="bg-slate-700/30 p-4 rounded-lg">
                    <h4 class="text-sm font-medium text-slate-200 mb-2">Screenshots</h4>
                    <div class="grid grid-cols-2 gap-4 mt-3">
                        ${service.screenshots.map(screenshot => `
                            <div class="bg-slate-800 rounded overflow-hidden">
                                <img src="${screenshot}" alt="${service.name} screenshot" class="w-full h-auto">
                            </div>
                        `).join('')}
                    </div>
                </div>
                ` : ''}
            </div>
        `;

        // Set action buttons
        if (isInstalled) {
            actionButtons.innerHTML = `
                <button class="bg-slate-700/50 hover:bg-red-900/50 text-slate-300 hover:text-red-200 rounded-md px-4 py-3 text-lg font-medium remove-service-btn">
                    <i data-lucide="trash-2" class="h-5 w-5 mr-2 inline"></i>
                    Remove from Dashboard
                </button>
            `;

            // Add event listener to remove button
            setTimeout(() => {
                const removeBtn = actionButtons.querySelector('.remove-service-btn');
                if (removeBtn) {
                    removeBtn.addEventListener('click', () => {
                        this.removeService(serviceId);
                        modal.classList.add('hidden');
                    });
                }
            }, 0);
        } else {
            actionButtons.innerHTML = `
                <button class="bg-cyan-600 hover:bg-cyan-500 text-white rounded-md px-4 py-3 text-lg font-medium install-service-btn">
                    <i data-lucide="plus-circle" class="h-5 w-5 mr-2 inline"></i>
                    Add to Dashboard
                </button>
            `;

            // Add event listener to install button
            setTimeout(() => {
                const installBtn = actionButtons.querySelector('.install-service-btn');
                if (installBtn) {
                    installBtn.addEventListener('click', () => {
                        this.installService(serviceId);
                        modal.classList.add('hidden');
                    });
                }
            }, 0);
        }

        // Initialize Lucide icons
        if (window.lucide) {
            lucide.createIcons();
        }

        // Show modal
        modal.classList.remove('hidden');

        // Add event listeners to close and back buttons
        closeButton.addEventListener('click', () => {
            modal.classList.add('hidden');
        });

        backButton.addEventListener('click', () => {
            modal.classList.add('hidden');
        });

        // Close modal when clicking outside
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.classList.add('hidden');
            }
        });
    }

    /**
     * Remove a service from the dashboard
     * @param {string} serviceId Service ID
     */
    async removeService(serviceId) {
        console.log(`Removing service: ${serviceId}`);

        try {
            // Find service in store
            const storeService = this.storeServices.find(s => s.id === serviceId);
            if (!storeService) {
                console.error(`Service ${serviceId} not found in store`);
                return;
            }

            // Check if service is installed
            if (!this.serviceManager.services.has(serviceId)) {
                console.warn(`Service ${serviceId} is not installed`);
                return;
            }

            // No confirmation needed

            // Remove service from the dashboard
            console.log('Removing service from dashboard:', serviceId);
            const wasRemoved = this.serviceManager.removeService(serviceId);

            if (!wasRemoved) {
                throw new Error(`Failed to remove service ${serviceId}`);
            }

            // Explicitly save service configurations to ensure they persist
            this.serviceManager.saveServiceConfigurations();
            console.log('Service configurations saved after removing service: success');

            // Update store display
            this.updateStoreDisplay();

            // Update the store info message visibility
            this.updateStoreInfoMessage();

            // Log success message
            console.log(`Service ${storeService.name} removed from dashboard successfully`);
        } catch (error) {
            console.error(`Failed to remove service ${serviceId}:`, error);
            alert(`Failed to remove service: ${error.message}`);
        }
    }




    /**
     * Get service ID from service name
     * @param {string} name Service name
     * @returns {string|null} Service ID or null if not found
     */
    getServiceIdFromName(name) {
        const serviceMap = {
            'KevkoHome': 'kevkoHome',
            'KevkoFy': 'kevkoFy',
            'KevkoCloud': 'kevkoCloud'
        };

        return serviceMap[name] || null;
    }

    /**
     * Load admin contacts data
     */
    async loadContacts() {
        try {
            const adminContactsContainer = document.getElementById('adminContactsContainer');
            if (!adminContactsContainer) {
                console.error('Admin contacts container not found');
                return;
            }

            // Show loading state
            adminContactsContainer.innerHTML = `
                <div class="flex justify-center items-center h-40 col-span-full">
                    <div class="flex items-center">
                        <i data-lucide="loader" class="h-5 w-5 text-slate-400 animate-spin mr-2"></i>
                        <span class="text-slate-400">Loading admin contacts...</span>
                    </div>
                </div>
            `;

            // Initialize icons
            if (window.lucide) lucide.createIcons();

            // Fetch admin contacts
            const response = await fetch('/api/admin/contacts');
            const admins = await response.json();

            if (response.ok && admins.length > 0) {
                // Clear loading state
                adminContactsContainer.innerHTML = '';

                // Add each admin contact
                admins.forEach(admin => {
                    const adminCard = document.createElement('div');
                    adminCard.className = 'bg-slate-800/50 rounded-lg overflow-hidden border border-slate-700/50';

                    // Determine admin badge style
                    const badgeStyle = admin.is_primary ?
                        'bg-red-500/20 text-red-400' :
                        'bg-cyan-500/20 text-cyan-400';

                    const badgeText = admin.is_primary ? 'Primary Admin' : 'Admin';

                    // Format name
                    const displayName = admin.name || admin.username;

                    // Create admin card HTML
                    adminCard.innerHTML = `
                        <div class="p-4">
                            <div class="flex items-center justify-between mb-3">
                                <div class="flex items-center space-x-3">
                                    ${admin.profile_picture ?
                                        `<img src="${admin.profile_picture}" alt="${displayName}" class="w-10 h-10 rounded-full object-cover">` :
                                        `<div class="w-10 h-10 rounded-full bg-slate-700 flex items-center justify-center text-slate-300 font-medium">
                                            ${displayName.substring(0, 2).toUpperCase()}
                                        </div>`
                                    }
                                    <div>
                                        <div class="text-sm font-medium text-slate-200">${displayName}</div>
                                        <div class="text-xs ${badgeStyle} px-1.5 py-0.5 rounded inline-block mt-1">${badgeText}</div>
                                    </div>
                                </div>
                            </div>

                            <div class="space-y-2">
                                <!-- Email -->
                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full bg-cyan-500/10 flex items-center justify-center text-cyan-400 mr-2">
                                        <i data-lucide="mail" class="h-4 w-4"></i>
                                    </div>
                                    <div>
                                        <div class="text-xs text-slate-400">Email</div>
                                        <a href="mailto:${admin.email}" class="text-sm text-cyan-400 hover:text-cyan-300 transition-colors">
                                            ${admin.email}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="bg-slate-700/30 p-3 flex justify-center">
                            <a href="mailto:${admin.email}?subject=Kevko%20Systems%20Support%20Request" class="text-sm text-slate-300 hover:text-white transition-colors flex items-center">
                                <i data-lucide="mail-plus" class="h-4 w-4 mr-1"></i>
                                Contact for Support
                            </a>
                        </div>
                    `;

                    adminContactsContainer.appendChild(adminCard);
                });

                // Initialize icons
                if (window.lucide) lucide.createIcons();
            } else {
                // Show error or no admins message
                adminContactsContainer.innerHTML = `
                    <div class="flex justify-center items-center h-40 col-span-full">
                        <div class="text-center">
                            <i data-lucide="alert-circle" class="h-8 w-8 text-slate-400 mx-auto mb-2"></i>
                            <p class="text-slate-400">No admin contacts available</p>
                        </div>
                    </div>
                `;

                // Initialize icons
                if (window.lucide) lucide.createIcons();
            }
        } catch (error) {
            console.error('Error loading admin contacts:', error);

            const adminContactsContainer = document.getElementById('adminContactsContainer');
            if (adminContactsContainer) {
                adminContactsContainer.innerHTML = `
                    <div class="flex justify-center items-center h-40 col-span-full">
                        <div class="text-center">
                            <i data-lucide="alert-triangle" class="h-8 w-8 text-red-400 mx-auto mb-2"></i>
                            <p class="text-red-400">Failed to load admin contacts</p>
                        </div>
                    </div>
                `;

                // Initialize icons
                if (window.lucide) lucide.createIcons();
            }
        }
    }

    /**
     * Set up contact management
     */
    setupContactManagement() {
        const addContactBtn = document.getElementById('addContactBtn');

        if (addContactBtn) {
            addContactBtn.addEventListener('click', () => {
                this.showAddContactModal();
            });
        }
    }

    /**
     * Show modal for adding a contact
     */
    showAddContactModal() {
        // Create modal if it doesn't exist
        let modal = document.getElementById('addContactModal');

        if (!modal) {
            modal = document.createElement('div');
            modal.id = 'addContactModal';
            modal.className = 'fixed inset-0 bg-black/50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-slate-800 rounded-lg p-6 max-w-md w-full mx-4">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-slate-100">Add Contact Method</h3>
                        <button id="closeContactModal" class="text-slate-400 hover:text-slate-100">
                            <i data-lucide="x" class="h-5 w-5"></i>
                        </button>
                    </div>
                    <form id="addContactForm" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-1">Contact Type</label>
                            <select id="contactType" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500">
                                <option value="email">Email</option>
                                <option value="phone">Phone</option>
                                <option value="discord">Discord</option>
                                <option value="github">GitHub</option>
                                <option value="twitter">Twitter</option>
                                <option value="linkedin">LinkedIn</option>
                                <option value="website">Website</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-1">Label</label>
                            <input type="text" id="contactLabel" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-1">Value</label>
                            <input type="text" id="contactValue" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500" required>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-slate-300 mb-1">Link (optional)</label>
                            <input type="url" id="contactLink" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500" placeholder="https://...">
                        </div>
                        <button type="submit" class="w-full bg-cyan-600 hover:bg-cyan-500 text-white rounded-md px-4 py-2 flex items-center justify-center">
                            <i data-lucide="plus" class="h-4 w-4 mr-2"></i>
                            Add Contact
                        </button>
                    </form>
                </div>
            `;

            document.body.appendChild(modal);

            // Refresh icons
            if (window.lucide) {
                lucide.createIcons();
            }

            // Add event listeners
            const closeButton = document.getElementById('closeContactModal');
            const form = document.getElementById('addContactForm');
            const typeSelect = document.getElementById('contactType');
            const labelInput = document.getElementById('contactLabel');

            if (closeButton) {
                closeButton.addEventListener('click', () => {
                    modal.remove();
                });
            }

            if (typeSelect && labelInput) {
                // Update label based on type
                typeSelect.addEventListener('change', () => {
                    const type = typeSelect.value;
                    labelInput.value = type.charAt(0).toUpperCase() + type.slice(1);
                });

                // Set initial label
                labelInput.value = 'Email';
            }

            if (form) {
                form.addEventListener('submit', (e) => {
                    e.preventDefault();

                    // Get form values
                    const type = document.getElementById('contactType').value;
                    const label = document.getElementById('contactLabel').value;
                    const value = document.getElementById('contactValue').value;
                    const link = document.getElementById('contactLink').value || null;

                    // Get icon and color based on type
                    const iconMap = {
                        'email': 'mail',
                        'phone': 'phone',
                        'discord': 'message-square',
                        'github': 'github',
                        'twitter': 'twitter',
                        'linkedin': 'linkedin',
                        'website': 'globe',
                        'other': 'link'
                    };

                    const colorMap = {
                        'email': 'cyan',
                        'phone': 'green',
                        'discord': 'purple',
                        'github': 'slate',
                        'twitter': 'blue',
                        'linkedin': 'blue',
                        'website': 'amber',
                        'other': 'slate'
                    };

                    // Create contact
                    const contact = {
                        id: Date.now(),
                        type,
                        icon: iconMap[type] || 'link',
                        color: colorMap[type] || 'slate',
                        label,
                        value,
                        link
                    };

                    // Add contact
                    this.addContact(contact);

                    // Close modal
                    modal.remove();
                });
            }
        } else {
            // Show existing modal
            modal.classList.remove('hidden');
        }
    }

    /**
     * Add a contact
     * @param {Object} contact Contact object
     */
    addContact(contact) {
        // Add contact to array
        this.contacts.push(contact);

        // Save contacts to localStorage
        this.saveContacts();

        // Update display
        this.updateContactsDisplay();
    }

    /**
     * Delete a contact
     * @param {number} id Contact ID
     */
    deleteContact(id) {
        // Remove contact from array
        this.contacts = this.contacts.filter(contact => contact.id !== id);

        // Save contacts to localStorage
        this.saveContacts();

        // Update display
        this.updateContactsDisplay();
    }

    /**
     * Save contacts to localStorage
     */
    saveContacts() {
        localStorage.setItem('contacts', JSON.stringify(this.contacts));
    }

    /**
     * Set up user management
     */
    setupUserManagement() {
        // Check if user manager is available
        if (!window.userManager) {
            console.error('UserManager not available');
            return;
        }

        // Populate user grid
        this.updateUserManagementUI();

        // Set up user action buttons
        this.setupUserActionButtons();
    }

    /**
     * Update user management UI
     */
    updateUserManagementUI() {
        const userGrid = document.querySelector('#adminSettingsSection .grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4');

        if (!userGrid) {
            console.error('User grid not found');
            return;
        }

        // Get all users
        const users = window.userManager.getAllUsers();

        // Add click handlers to user action buttons
        userGrid.querySelectorAll('.bg-slate-700\\/30.rounded-lg').forEach((userCard, index) => {
            if (index >= users.length) return;

            const user = users[index];

            // Set user ID as data attribute
            userCard.setAttribute('data-user-id', user.id);

            // Update user stats
            const statsContainers = userCard.querySelectorAll('.grid.grid-cols-2.gap-2 .bg-slate-800\\/50.p-2');
            if (statsContainers.length >= 4) {
                // Update services count
                const servicesCount = window.userManager.getUserActiveServices(user.id);
                statsContainers[0].querySelector('.text-slate-200.font-medium').textContent = `${servicesCount} Active`;

                // Update friends count
                const friendsCount = window.userManager.getUserFriends(user.id).length;
                statsContainers[1].querySelector('.text-slate-200.font-medium').textContent = `${friendsCount} Connected`;
            }

            // Update user activity
            const activityContainer = userCard.querySelector('.space-y-1.max-h-24.overflow-y-auto');
            if (activityContainer) {
                const activities = window.userManager.getUserActivity(user.id, 3);
                activityContainer.innerHTML = activities.map(activity => {
                    const timeString = window.userManager.formatRelativeTime(activity.timestamp);
                    return `
                        <div class="flex justify-between">
                            <span class="text-slate-300">${activity.service}</span>
                            <span class="text-slate-400">${timeString}</span>
                        </div>
                    `;
                }).join('');
            }

            // Set up action buttons
            const detailsBtn = userCard.querySelector('button:nth-of-type(1)');
            const editBtn = userCard.querySelector('button:nth-of-type(2)');
            const lockBtn = userCard.querySelector('button:nth-of-type(3)');

            if (detailsBtn) {
                detailsBtn.addEventListener('click', () => {
                    this.showUserDetailsModal(user.id);
                });
            }

            if (editBtn) {
                editBtn.addEventListener('click', () => {
                    this.showUserEditModal(user.id);
                });
            }

            if (lockBtn) {
                lockBtn.addEventListener('click', () => {
                    this.toggleUserLock(user.id);
                });
            }
        });
    }

    /**
     * Set up user action buttons
     */
    setupUserActionButtons() {
        // View all users button
        const viewAllUsersBtn = document.querySelector('#adminSettingsSection button:has(i[data-lucide="users"])');
        if (viewAllUsersBtn) {
            viewAllUsersBtn.addEventListener('click', () => {
                this.showAllUsersModal();
            });
        }

        // Add user button
        const addUserBtn = document.querySelector('#adminSettingsSection button:has(i[data-lucide="plus"]):not([id])');
        if (addUserBtn) {
            addUserBtn.addEventListener('click', () => {
                this.showAddUserModal();
            });
        }
    }

    /**
     * Show user details modal
     * @param {number} userId User ID
     */
    showUserDetailsModal(userId) {
        const user = window.userManager.getUserById(userId);
        if (!user) {
            console.error(`User ${userId} not found`);
            return;
        }

        alert(`User details for ${user.name} (${user.username}) will be shown here.`);
        // In a real implementation, this would show a modal with detailed user information
    }

    /**
     * Show user edit modal
     * @param {number} userId User ID
     */
    showUserEditModal(userId) {
        const user = window.userManager.getUserById(userId);
        if (!user) {
            console.error(`User ${userId} not found`);
            return;
        }

        alert(`Edit user ${user.name} (${user.username}) will be implemented here.`);
        // In a real implementation, this would show a modal for editing user information
    }

    /**
     * Toggle user lock status
     * @param {number} userId User ID
     */
    toggleUserLock(userId) {
        const user = window.userManager.getUserById(userId);
        if (!user) {
            console.error(`User ${userId} not found`);
            return;
        }

        alert(`Toggle lock for user ${user.name} (${user.username}) will be implemented here.`);
        // In a real implementation, this would toggle the user's lock status
    }

    /**
     * Show all users modal
     */
    showAllUsersModal() {
        alert('View all users will be implemented here.');
        // In a real implementation, this would show a modal with all users
    }

    /**
     * Show add user modal
     */
    showAddUserModal() {
        alert('Add user will be implemented here.');
        // In a real implementation, this would show a modal for adding a new user
    }

    /**

    /**
     * Set up service restrictions
     */
    setupServiceRestrictions() {
        // Check if user is an admin before setting up service restrictions
        if (!this.isUserAdmin) {
            console.log('Service restrictions setup skipped - user is not an admin');
            return;
        }

        // Check if service restrictions is available
        if (!window.serviceRestrictions) {
            console.error('ServiceRestrictions not available');
            return;
        }

        // Set up service selector
        this.setupServiceRestrictionsSelector();

        // Set up restriction type buttons
        this.setupRestrictionTypeButtons();
    }

    /**
     * Set up service restrictions selector
     */
    setupServiceRestrictionsSelector() {
        const serviceSelector = document.querySelector('#adminSettingsSection select');

        if (!serviceSelector) {
            console.error('Service selector not found');
            return;
        }

        // Add change handler
        serviceSelector.addEventListener('change', () => {
            const serviceId = serviceSelector.value;
            this.updateServiceRestrictionsUI(serviceId);
        });

        // Initialize with first service
        this.updateServiceRestrictionsUI(serviceSelector.value);
    }

    /**
     * Set up restriction type buttons
     */
    setupRestrictionTypeButtons() {
        // Get all buttons in the admin settings section
        const restrictionCards = document.querySelectorAll('#adminSettingsSection .bg-slate-700\\/30.rounded-lg');

        restrictionCards.forEach(card => {
            // Find edit buttons by iterating through all buttons and checking their text content
            const buttons = card.querySelectorAll('button');
            buttons.forEach(button => {
                if (button.textContent.trim() === 'Edit') {
                    button.addEventListener('click', () => {
                        const restrictionCard = button.closest('.bg-slate-700\\/30.rounded-lg');
                        const restrictionType = restrictionCard.querySelector('.text-sm.font-medium.text-slate-200')?.textContent;
                        const serviceSelector = document.querySelector('#adminSettingsSection select');
                        const serviceId = serviceSelector?.value;

                        if (restrictionType && serviceId) {
                            this.editRestriction(serviceId, restrictionType);
                        }
                    });
                }
            });
        });

        // Get all restriction type delete buttons
        restrictionCards.forEach(card => {
            // Find delete buttons by iterating through all buttons and checking their text content
            const buttons = card.querySelectorAll('button');
            buttons.forEach(button => {
                if (button.textContent.trim() === '×') {
                    button.addEventListener('click', () => {
                        const restrictionCard = button.closest('.bg-slate-700\\/30.rounded-lg');
                        const restrictionType = restrictionCard.querySelector('.text-sm.font-medium.text-slate-200')?.textContent;
                        const serviceSelector = document.querySelector('#adminSettingsSection select');
                        const serviceId = serviceSelector?.value;

                        if (restrictionType && serviceId) {
                            this.deleteRestriction(serviceId, restrictionType);
                        }
                    });
                }
            });
        });

        // Get all add user/rule buttons
        restrictionCards.forEach(card => {
            // Find add buttons by iterating through all buttons and checking their text content
            const buttons = card.querySelectorAll('button');
            buttons.forEach(button => {
                if (button.textContent.trim().includes('Add') && button.textContent.trim() !== 'Add New Restriction Type') {
                    button.addEventListener('click', () => {
                        const restrictionCard = button.closest('.bg-slate-700\\/30.rounded-lg');
                        const restrictionType = restrictionCard.querySelector('.text-sm.font-medium.text-slate-200')?.textContent;
                        const serviceSelector = document.querySelector('#adminSettingsSection select');
                        const serviceId = serviceSelector?.value;

                        if (restrictionType && serviceId) {
                            this.addRestrictionItem(serviceId, restrictionType, button.textContent.trim());
                        }
                    });
                }
            });
        });

        // Get add new restriction type button
        const buttons = document.querySelectorAll('#adminSettingsSection button');
        let addRestrictionButton = null;

        // Find the button with text "Add New Restriction Type"
        buttons.forEach(button => {
            if (button.textContent.trim() === 'Add New Restriction Type') {
                addRestrictionButton = button;
            }
        });

        if (addRestrictionButton) {
            addRestrictionButton.addEventListener('click', () => {
                const serviceSelector = document.querySelector('#adminSettingsSection select');
                const serviceId = serviceSelector?.value;

                if (serviceId) {
                    this.addNewRestrictionType(serviceId);
                }
            });
        }
    }

    /**
     * Update service restrictions UI
     * @param {string} serviceId Service ID
     */
    updateServiceRestrictionsUI(serviceId) {
        if (serviceId === 'all') {
            // Instead of showing alert, just return or show all services
            return;
        }

        // Get restrictions for the selected service
        const restrictions = window.serviceRestrictions.getServiceRestrictions(serviceId);

        // Update UI based on restrictions
        this.updateStorageLimitsUI(serviceId, restrictions?.storageLimits);
        this.updateTimeRestrictionsUI(serviceId, restrictions?.timeRestrictions);
        this.updateFeatureRestrictionsUI(serviceId, restrictions?.featureRestrictions);
    }





    /**
     * Update storage limits UI
     * @param {string} serviceId Service ID
     * @param {Object} storageLimits Storage limits object
     */
    updateStorageLimitsUI(serviceId, storageLimits) {
        // In a real implementation, this would update the UI based on the storage limits
        console.log(`Updating storage limits UI for service ${serviceId}:`, storageLimits);
    }

    /**
     * Update time restrictions UI
     * @param {string} serviceId Service ID
     * @param {Object} timeRestrictions Time restrictions object
     */
    updateTimeRestrictionsUI(serviceId, timeRestrictions) {
        // In a real implementation, this would update the UI based on the time restrictions
        console.log(`Updating time restrictions UI for service ${serviceId}:`, timeRestrictions);
    }

    /**
     * Update feature restrictions UI
     * @param {string} serviceId Service ID
     * @param {Object} featureRestrictions Feature restrictions object
     */
    updateFeatureRestrictionsUI(serviceId, featureRestrictions) {
        // In a real implementation, this would update the UI based on the feature restrictions
        console.log(`Updating feature restrictions UI for service ${serviceId}:`, featureRestrictions);
    }

    /**
     * Edit restriction
     * @param {string} serviceId Service ID
     * @param {string} restrictionType Restriction type
     */
    editRestriction(serviceId, restrictionType) {
        // Silently do nothing for now - future implementation will add a modal
        console.log(`Feature not yet implemented: Edit ${restrictionType} for service ${serviceId}`);
        // In a real implementation, this would show a modal for editing the restriction
    }

    /**
     * Delete restriction
     * @param {string} serviceId Service ID
     * @param {string} restrictionType Restriction type
     */
    deleteRestriction(serviceId, restrictionType) {
        if (confirm(`Are you sure you want to delete the ${restrictionType} restriction for service ${serviceId}?`)) {
            // Silently do nothing for now - future implementation will delete the restriction
            console.log(`Feature not yet implemented: Delete ${restrictionType} for service ${serviceId}`);
            // In a real implementation, this would delete the restriction
        }
    }

    /**
     * Add restriction item
     * @param {string} serviceId Service ID
     * @param {string} restrictionType Restriction type
     * @param {string} buttonText Button text
     */
    addRestrictionItem(serviceId, restrictionType, buttonText) {
        console.log(`Adding ${buttonText} to ${restrictionType} for service ${serviceId}`);

        // Silently do nothing for now - future implementation will add specific modals
        console.log(`Feature not yet implemented: Add ${buttonText} to ${restrictionType} for service ${serviceId}`);
        // In a real implementation, this would show a modal for adding a new item to the restriction
    }

    /**
     * Add new restriction type
     * @param {string} serviceId Service ID
     */
    addNewRestrictionType(serviceId) {
        // Silently do nothing for now - future implementation will add a modal
        console.log(`Feature not yet implemented: Add new restriction type for service ${serviceId}`);
        // In a real implementation, this would show a modal for adding a new restriction type
    }

    /**
     * Show modal for adding a restricted user to a service
     * @param {string} serviceId Service ID
     */
    showAddRestrictedUserModal(serviceId) {
        console.log(`Showing add restricted user modal for service: ${serviceId}`);

        // Check if user can make admin requests
        if (!this.canMakeAdminRequests()) {
            this.showToast('Admin access required to perform this action', 'error');
            return;
        }

        // Create modal
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black/70 flex items-center justify-center z-50';
        modal.id = 'addRestrictedUserModal';

        modal.innerHTML = `
            <div class="bg-slate-800 rounded-lg p-6 w-96 max-w-full">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-slate-100">Add User Restriction</h3>
                    <button id="closeRestrictedUserModal" class="text-slate-400 hover:text-slate-100">
                        <i data-lucide="x" class="h-5 w-5"></i>
                    </button>
                </div>
                <div class="space-y-4">
                    <p class="text-slate-300 text-sm">Enter the email of the user you want to restrict from using ${serviceId === 'all' ? 'services' : `the ${serviceId} service`}.</p>
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-1">Email</label>
                        <input type="email" id="restrictedUserEmail" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500" required>
                    </div>
                    <div class="pt-2">
                        <button type="button" id="confirmAddRestrictedUserBtn" class="w-full bg-cyan-600 hover:bg-cyan-500 text-white rounded-md px-4 py-2 flex items-center justify-center">
                            <i data-lucide="user-plus" class="h-4 w-4 mr-2"></i>
                            Add User Restriction
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Refresh icons
        if (window.lucide) {
            lucide.createIcons();
        }

        // Add event listeners
        const closeButton = document.getElementById('closeRestrictedUserModal');
        if (closeButton) {
            closeButton.addEventListener('click', () => {
                modal.remove();
            });
        }

        // Add event listener to confirm button
        const confirmButton = document.getElementById('confirmAddRestrictedUserBtn');
        const emailInput = document.getElementById('restrictedUserEmail');

        if (confirmButton && emailInput) {
            confirmButton.addEventListener('click', () => {
                const email = emailInput.value.trim();

                if (!email) {
                    this.showToast('Please enter a valid email address', 'error');
                    return;
                }

                // Show loading state
                const originalButtonText = confirmButton.innerHTML;
                confirmButton.innerHTML = `<i data-lucide="loader" class="h-4 w-4 mr-2 animate-spin"></i>Adding Restriction...`;
                confirmButton.disabled = true;

                // Initialize icons
                if (window.lucide) {
                    lucide.createIcons({
                        attrs: {
                            class: ["h-4", "w-4"]
                        },
                        elements: [confirmButton]
                    });
                }

                // Call the API to add restriction
                fetch('/api/admin/restrict-user', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        services: serviceId === 'all' ? ['chat', 'live', 'spotify', 'friends'] : [serviceId]
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        this.showToast(data.error, 'error');
                        confirmButton.innerHTML = originalButtonText;
                        confirmButton.disabled = false;
                        if (window.lucide) lucide.createIcons();
                    } else {
                        this.showToast(data.message || 'User restriction added successfully');
                        modal.remove();

                        // Reload restrictions list if available
                        if (typeof this.loadServiceRestrictions === 'function') {
                            this.loadServiceRestrictions(serviceId);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error adding user restriction:', error);
                    this.showToast('Failed to add user restriction. Please try again.', 'error');
                    confirmButton.innerHTML = originalButtonText;
                    confirmButton.disabled = false;
                    if (window.lucide) lucide.createIcons();
                });
            });
        }
    }



    /**
     * Set up settings
     */
    setupSettings() {
        // Check if settings manager is available
        if (!window.settingsManager) {
            console.error('SettingsManager not available');
            return;
        }

        // Load current settings
        this.loadCurrentSettings();

        // Set up appearance settings
        this.setupAppearanceSettings();

        // Set up notification settings
        this.setupNotificationSettings();

        // Set up privacy settings
        this.setupPrivacySettings();

        // Set up account settings
        this.setupAccountSettings();

        // Set up connected services
        this.setupConnectedServices();

        // Reset settings tabs to ensure only the active tab's content is shown
        this.resetSettingsTabs();
    }

    /**
     * Set up responsive handling for window resize events
     */
    setupResponsiveHandling() {
        // Store initial window size
        this.windowWidth = window.innerWidth;
        this.windowHeight = window.innerHeight;

        // Handle window resize events
        window.addEventListener('resize', this.handleWindowResize.bind(this));

        // Initial responsive adjustments
        this.adjustUIForScreenSize();

        // Handle orientation change on mobile devices
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.adjustUIForScreenSize();
            }, 300); // Small delay to ensure orientation change is complete
        });
    }

    /**
     * Set up global search functionality
     */
    setupGlobalSearch() {
        const searchInput = document.getElementById('globalSearchInput');
        const searchResults = document.getElementById('globalSearchResults');
        const searchContent = document.getElementById('globalSearchContent');
        const closeButton = document.getElementById('closeGlobalSearch');
        const mobileSearchIcon = document.getElementById('mobileSearchIcon');

        if (!searchInput || !searchResults || !searchContent) {
            console.error('Global search elements not found');
            return;
        }

        // Add event listener for mobile search icon
        if (mobileSearchIcon) {
            console.log('Setting up mobile search icon click handler');
            mobileSearchIcon.addEventListener('click', (e) => {
                console.log('Mobile search icon clicked');
                e.preventDefault();
                e.stopPropagation();

                // Toggle the search input visibility on mobile
                if (window.innerWidth < 768) {
                    const searchContainer = document.getElementById('searchContainer');
                    searchInput.classList.toggle('hidden');

                    if (!searchInput.classList.contains('hidden')) {
                        // Expand the container when search is visible
                        if (searchContainer) {
                            searchContainer.classList.add('w-36');
                        }
                        setTimeout(() => searchInput.focus(), 50);
                    } else {
                        // Restore container when search is hidden
                        if (searchContainer) {
                            searchContainer.classList.remove('w-36');
                        }
                    }
                } else {
                    // On desktop, just focus the input
                    searchInput.focus();
                }
            });
        } else {
            console.error('Mobile search icon not found');
        }

        // Add event listener for search input
        let searchTimeout;
        searchInput.addEventListener('input', () => {
            // Clear previous timeout
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }

            const query = searchInput.value.trim().toLowerCase();

            // Hide search results if query is empty
            if (query.length === 0) {
                searchResults.classList.add('hidden');
                return;
            }

            // Set a timeout to avoid searching on every keystroke
            searchTimeout = setTimeout(() => {
                this.performGlobalSearch(query);
            }, 300);
        });

        // Add event listener for close button
        if (closeButton) {
            closeButton.addEventListener('click', () => {
                searchResults.classList.add('hidden');
            });
        }

        // Close search results and input when clicking outside
        document.addEventListener('click', (e) => {
            // Close search results if they're open
            if (!searchInput.contains(e.target) &&
                !searchResults.contains(e.target) &&
                !searchResults.classList.contains('hidden')) {
                searchResults.classList.add('hidden');
            }

            // On mobile, also close the search input if it's open
            if (window.innerWidth < 768 &&
                !searchInput.classList.contains('hidden') &&
                !searchInput.contains(e.target) &&
                !mobileSearchIcon.contains(e.target)) {
                searchInput.classList.add('hidden');
                const searchContainer = document.getElementById('searchContainer');
                if (searchContainer) {
                    searchContainer.classList.remove('w-36');
                }
            }
        });

        // Prevent clicks inside search results from closing it
        searchResults.addEventListener('click', (e) => {
            e.stopPropagation();
        });
    }

    /**
     * Perform global search across different categories
     * @param {string} query Search query
     */
    performGlobalSearch(query) {
        console.log(`Performing global search for: ${query}`);
        const searchResults = document.getElementById('globalSearchResults');
        const searchContent = document.getElementById('globalSearchContent');

        if (!searchResults || !searchContent) {
            console.error('Global search elements not found');
            return;
        }

        // Show loading state
        searchContent.innerHTML = `
            <div class="flex justify-center items-center py-4">
                <div class="flex items-center">
                    <i data-lucide="loader" class="h-5 w-5 text-slate-400 animate-spin mr-2"></i>
                    <span class="text-slate-400 text-sm">Searching...</span>
                </div>
            </div>
        `;

        // Initialize Lucide icons
        if (window.lucide) {
            lucide.createIcons({
                attrs: {
                    class: ["h-5", "w-5"]
                },
                elements: [searchContent]
            });
        }

        // Show search results
        searchResults.classList.remove('hidden');

        // Perform search across different categories
        const results = {
            services: this.searchServices(query),
            settings: this.searchSettings(query),
            updates: this.searchUpdates(query),
            profile: this.searchProfileOptions(query),
            admin: this.isUserAdmin ? this.searchAdminOptions(query) : []
        };

        // Display search results
        this.displaySearchResults(results);
    }

    /**
     * Search for services matching the query
     * @param {string} query Search query
     * @returns {Array} Array of matching services
     */
    searchServices(query) {
        const results = [];

        // Search in installed services
        this.serviceManager.services.forEach(service => {
            if (service.name.toLowerCase().includes(query) ||
                service.description.toLowerCase().includes(query)) {
                results.push({
                    id: service.id,
                    name: service.name,
                    description: service.description,
                    icon: service.icon,
                    color: service.color,
                    installed: true
                });
            }
        });

        // Search in store services
        this.storeServices.forEach(service => {
            // Check if service is already in results (from installed services)
            const existingService = results.find(s => s.id === service.id);
            if (!existingService &&
                (service.name.toLowerCase().includes(query) ||
                service.description.toLowerCase().includes(query))) {
                results.push({
                    id: service.id,
                    name: service.name,
                    description: service.description,
                    icon: service.icon,
                    color: service.color,
                    installed: false
                });
            }
        });

        return results;
    }

    /**
     * Search for settings matching the query
     * @param {string} query Search query
     * @returns {Array} Array of matching settings
     */
    searchSettings(query) {
        const settingsItems = [
            {
                id: 'notifications',
                name: 'Notification Settings',
                description: 'Configure how you receive notifications',
                icon: 'bell',
                color: 'cyan',
                section: 'preferences'
            },
            {
                id: 'privacy',
                name: 'Privacy Settings',
                description: 'Control your privacy and what information is shared',
                icon: 'shield',
                color: 'cyan',
                section: 'preferences'
            },
            {
                id: 'theme',
                name: 'Theme Settings',
                description: 'Customize the look and feel of your dashboard',
                icon: 'palette',
                color: 'cyan',
                section: 'appearance'
            },
            {
                id: 'text',
                name: 'Text Settings',
                description: 'Adjust text size throughout the application',
                icon: 'type',
                color: 'cyan',
                section: 'appearance'
            },
            {
                id: 'connections',
                name: 'External Connections',
                description: 'Connect external services to enhance your experience',
                icon: 'link',
                color: 'cyan',
                section: 'connections'
            }
        ];

        // Filter settings based on query
        return settingsItems.filter(item =>
            item.name.toLowerCase().includes(query) ||
            item.description.toLowerCase().includes(query)
        );
    }

    /**
     * Search for updates matching the query
     * @param {string} query Search query
     * @returns {Array} Array of matching updates
     */
    searchUpdates(query) {
        // Filter updates based on query
        return this.updates.filter(update =>
            update.title.toLowerCase().includes(query) ||
            update.description.toLowerCase().includes(query) ||
            update.service.toLowerCase().includes(query)
        );
    }

    /**
     * Search for profile options matching the query
     * @param {string} query Search query
     * @returns {Array} Array of matching profile options
     */
    searchProfileOptions(query) {
        const profileOptions = [
            {
                id: 'personal-info',
                name: 'Personal Information',
                description: 'Update your name, email, and profile picture',
                icon: 'user',
                color: 'blue',
                section: 'profile'
            },
            {
                id: 'account-security',
                name: 'Account Security',
                description: 'Manage your password and security settings',
                icon: 'shield',
                color: 'blue',
                section: 'profile'
            },
            {
                id: 'notification-preferences',
                name: 'Notification Preferences',
                description: 'Control how you receive notifications',
                icon: 'bell',
                color: 'blue',
                section: 'profile'
            },
            {
                id: 'connected-accounts',
                name: 'Connected Accounts',
                description: 'Manage your connected social accounts',
                icon: 'link',
                color: 'blue',
                section: 'profile'
            },
            {
                id: 'activity-log',
                name: 'Activity Log',
                description: 'View your recent account activity',
                icon: 'activity',
                color: 'blue',
                section: 'profile'
            }
        ];

        // Filter profile options based on query
        return profileOptions.filter(option =>
            option.name.toLowerCase().includes(query) ||
            option.description.toLowerCase().includes(query)
        );
    }

    /**
     * Search for admin options matching the query
     * @param {string} query Search query
     * @returns {Array} Array of matching admin options
     */
    searchAdminOptions(query) {
        const adminOptions = [
            {
                id: 'user-management',
                name: 'User Management',
                description: 'Manage users, roles, and permissions',
                icon: 'users',
                color: 'red',
                section: 'admin'
            },
            {
                id: 'service-restrictions',
                name: 'Service Restrictions',
                description: 'Control access to services for specific users',
                icon: 'shield-alert',
                color: 'red',
                section: 'admin'
            },
            {
                id: 'system-logs',
                name: 'System Logs',
                description: 'View system logs and activity',
                icon: 'file-text',
                color: 'red',
                section: 'admin'
            },
            {
                id: 'system-settings',
                name: 'System Settings',
                description: 'Configure global system settings',
                icon: 'settings',
                color: 'red',
                section: 'admin'
            },
            {
                id: 'active-users',
                name: 'Active Users',
                description: 'Monitor currently active users',
                icon: 'user-check',
                color: 'red',
                section: 'admin'
            },
            {
                id: 'admin-panel',
                name: 'Admin Panel',
                description: 'Access the main admin dashboard',
                icon: 'layout-dashboard',
                color: 'red',
                section: 'admin'
            }
        ];

        // Filter admin options based on query
        return adminOptions.filter(option =>
            option.name.toLowerCase().includes(query) ||
            option.description.toLowerCase().includes(query)
        );
    }

    /**
     * Display search results
     * @param {Object} results Search results object
     */
    displaySearchResults(results) {
        const searchContent = document.getElementById('globalSearchContent');
        if (!searchContent) {
            console.error('Search content element not found');
            return;
        }

        // Check if there are any results
        const totalResults = results.services.length + results.settings.length +
                            results.updates.length + results.profile.length + results.admin.length;

        if (totalResults === 0) {
            searchContent.innerHTML = `
                <div class="flex flex-col items-center justify-center py-6">
                    <i data-lucide="search-x" class="h-8 w-8 text-slate-500 mb-2"></i>
                    <p class="text-slate-400">No results found</p>
                </div>
            `;
        } else {
            // Build results HTML
            let html = '';

            // Services section
            if (results.services.length > 0) {
                html += `
                    <div class="result-section">
                        <h4 class="text-sm font-medium text-slate-300 mb-2 flex items-center">
                            <i data-lucide="layout-grid" class="h-4 w-4 mr-2 text-cyan-500"></i>
                            Services (${results.services.length})
                        </h4>
                        <div class="space-y-2">
                `;

                results.services.forEach(service => {
                    html += `
                        <div class="service-result bg-slate-700/30 rounded-lg p-3 cursor-pointer hover:bg-slate-700/50 transition-colors" data-service-id="${service.id}">
                            <div class="flex items-start">
                                <div class="p-2 rounded-lg bg-${service.color}-500/10 mr-3">
                                    <i data-lucide="${service.icon}" class="h-4 w-4 text-${service.color}-500"></i>
                                </div>
                                <div class="flex-grow">
                                    <div class="flex items-center justify-between">
                                        <h5 class="text-sm font-medium text-slate-200">${service.name}</h5>
                                        ${service.installed ?
                                            `<span class="text-xs bg-${service.color}-500/20 text-${service.color}-400 px-1.5 py-0.5 rounded-full">Installed</span>` :
                                            `<span class="text-xs bg-slate-600/30 text-slate-400 px-1.5 py-0.5 rounded-full">Available</span>`
                                        }
                                    </div>
                                    <p class="text-xs text-slate-400 mt-1">${service.description}</p>
                                </div>
                            </div>
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
            }

            // Settings section
            if (results.settings.length > 0) {
                html += `
                    <div class="result-section mt-4">
                        <h4 class="text-sm font-medium text-slate-300 mb-2 flex items-center">
                            <i data-lucide="settings" class="h-4 w-4 mr-2 text-cyan-500"></i>
                            Settings (${results.settings.length})
                        </h4>
                        <div class="space-y-2">
                `;

                results.settings.forEach(setting => {
                    html += `
                        <div class="setting-result bg-slate-700/30 rounded-lg p-3 cursor-pointer hover:bg-slate-700/50 transition-colors" data-setting-id="${setting.id}" data-setting-section="${setting.section}">
                            <div class="flex items-start">
                                <div class="p-2 rounded-lg bg-${setting.color}-500/10 mr-3">
                                    <i data-lucide="${setting.icon}" class="h-4 w-4 text-${setting.color}-500"></i>
                                </div>
                                <div>
                                    <h5 class="text-sm font-medium text-slate-200">${setting.name}</h5>
                                    <p class="text-xs text-slate-400 mt-1">${setting.description}</p>
                                </div>
                            </div>
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
            }

            // Updates section
            if (results.updates.length > 0) {
                html += `
                    <div class="result-section mt-4">
                        <h4 class="text-sm font-medium text-slate-300 mb-2 flex items-center">
                            <i data-lucide="refresh-cw" class="h-4 w-4 mr-2 text-cyan-500"></i>
                            Updates (${results.updates.length})
                        </h4>
                        <div class="space-y-2">
                `;

                results.updates.forEach(update => {
                    html += `
                        <div class="update-result bg-slate-700/30 rounded-lg p-3">
                            <div class="flex items-start">
                                <div class="p-2 rounded-lg bg-${update.color}-500/10 mr-3">
                                    <i data-lucide="${update.icon}" class="h-4 w-4 text-${update.color}-500"></i>
                                </div>
                                <div>
                                    <div class="flex items-center justify-between">
                                        <h5 class="text-sm font-medium text-slate-200">${update.title}</h5>
                                        <span class="text-xs text-slate-500">${update.date}</span>
                                    </div>
                                    <p class="text-xs text-slate-300 mt-1">${update.service}</p>
                                    <p class="text-xs text-slate-400 mt-1">${update.description}</p>
                                </div>
                            </div>
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
            }

            // Profile options section
            if (results.profile.length > 0) {
                html += `
                    <div class="result-section mt-4">
                        <h4 class="text-sm font-medium text-slate-300 mb-2 flex items-center">
                            <i data-lucide="user" class="h-4 w-4 mr-2 text-blue-500"></i>
                            Profile (${results.profile.length})
                        </h4>
                        <div class="space-y-2">
                `;

                results.profile.forEach(option => {
                    html += `
                        <div class="profile-result bg-slate-700/30 rounded-lg p-3 cursor-pointer hover:bg-slate-700/50 transition-colors" data-profile-id="${option.id}">
                            <div class="flex items-start">
                                <div class="p-2 rounded-lg bg-${option.color}-500/10 mr-3">
                                    <i data-lucide="${option.icon}" class="h-4 w-4 text-${option.color}-500"></i>
                                </div>
                                <div>
                                    <h5 class="text-sm font-medium text-slate-200">${option.name}</h5>
                                    <p class="text-xs text-slate-400 mt-1">${option.description}</p>
                                </div>
                            </div>
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
            }

            // Admin options section
            if (results.admin.length > 0) {
                html += `
                    <div class="result-section mt-4">
                        <h4 class="text-sm font-medium text-slate-300 mb-2 flex items-center">
                            <i data-lucide="shield" class="h-4 w-4 mr-2 text-red-500"></i>
                            Admin (${results.admin.length})
                        </h4>
                        <div class="space-y-2">
                `;

                results.admin.forEach(option => {
                    html += `
                        <div class="admin-result bg-slate-700/30 rounded-lg p-3 cursor-pointer hover:bg-slate-700/50 transition-colors" data-admin-id="${option.id}">
                            <div class="flex items-start">
                                <div class="p-2 rounded-lg bg-${option.color}-500/10 mr-3">
                                    <i data-lucide="${option.icon}" class="h-4 w-4 text-${option.color}-500"></i>
                                </div>
                                <div>
                                    <h5 class="text-sm font-medium text-slate-200">${option.name}</h5>
                                    <p class="text-xs text-slate-400 mt-1">${option.description}</p>
                                </div>
                            </div>
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
            }

            searchContent.innerHTML = html;

            // Add event listeners to service results
            document.querySelectorAll('.service-result').forEach(element => {
                element.addEventListener('click', () => {
                    const serviceId = element.getAttribute('data-service-id');
                    if (serviceId) {
                        // Check if service is installed
                        const isInstalled = this.serviceManager.services.has(serviceId);

                        if (isInstalled) {
                            // Switch to dashboard view and focus on the service
                            this.switchView('dashboard');
                            // Highlight the service card (could add a pulse animation)
                            const serviceCard = document.getElementById(`service-${serviceId}`);
                            if (serviceCard) {
                                serviceCard.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                serviceCard.classList.add('highlight-pulse');
                                setTimeout(() => {
                                    serviceCard.classList.remove('highlight-pulse');
                                }, 2000);
                            }
                        } else {
                            // Show service details in store
                            this.switchView('store');
                            this.showServiceDetails(serviceId);
                        }

                        // Hide search results
                        document.getElementById('globalSearchResults').classList.add('hidden');
                    }
                });
            });

            // Add event listeners to setting results
            document.querySelectorAll('.setting-result').forEach(element => {
                element.addEventListener('click', () => {
                    const settingId = element.getAttribute('data-setting-id');
                    const settingSection = element.getAttribute('data-setting-section');

                    if (settingId && settingSection) {
                        console.log(`Setting result clicked: ${settingId} in section ${settingSection}`);

                        // Hide search results first
                        document.getElementById('globalSearchResults').classList.add('hidden');

                        // Switch to settings view
                        this.switchView('settings');

                        // Navigate to the specific setting
                        setTimeout(() => {
                            this.navigateToSettingSection(settingId, settingSection);
                        }, 300);
                    }
                });
            });

            // Helper method to navigate to setting sections
            this.navigateToSettingSection = (settingId, settingSection) => {
                console.log(`Navigating to setting: ${settingId} in section ${settingSection}`);

                // Special handling for specific settings
                if (settingId === 'notifications' && settingSection === 'user') {
                    // Try to find notifications settings directly
                    const notificationsSection = document.getElementById('notificationsSettings') ||
                                               document.querySelector('.notifications-settings') ||
                                               document.querySelector('[data-setting="notifications"]');

                    if (notificationsSection) {
                        // Make sure we're in the user tab
                        const userTab = document.getElementById('userSettingsTab') ||
                                      document.querySelector('[data-tab="user"]');
                        if (userTab) {
                            userTab.click();
                            setTimeout(() => {
                                notificationsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                                // Highlight the section
                                notificationsSection.classList.add('highlight-pulse');
                                setTimeout(() => {
                                    notificationsSection.classList.remove('highlight-pulse');
                                }, 2000);
                            }, 300);
                            return;
                        }
                    }
                }

                if (settingId === 'theme' && settingSection === 'appearance') {
                    // Try to find theme settings directly
                    const themeSection = document.getElementById('themeSettings') ||
                                       document.querySelector('.theme-settings') ||
                                       document.querySelector('[data-setting="theme"]');

                    if (themeSection) {
                        // Make sure we're in the appearance tab
                        const appearanceTab = document.getElementById('appearanceSettingsTab') ||
                                           document.querySelector('[data-tab="appearance"]');
                        if (appearanceTab) {
                            appearanceTab.click();
                            setTimeout(() => {
                                themeSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                                // Highlight the section
                                themeSection.classList.add('highlight-pulse');
                                setTimeout(() => {
                                    themeSection.classList.remove('highlight-pulse');
                                }, 2000);
                            }, 300);
                            return;
                        }
                    }
                }

                // Try to find and click the appropriate settings tab
                const tabSelectors = [
                    `#${settingSection}SettingsTab`,
                    `#${settingSection}Tab`,
                    `[data-tab="${settingSection}"]`,
                    `.settings-tab[data-section="${settingSection}"]`,
                    `button[data-settings-tab="${settingSection}"]`
                ];

                let tabFound = false;
                for (const selector of tabSelectors) {
                    try {
                        const tabButton = document.querySelector(selector);
                        if (tabButton) {
                            console.log(`Found settings tab with selector: ${selector}`);
                            tabButton.click();
                            tabFound = true;
                            break;
                        }
                    } catch (e) {
                        // Some selectors might be invalid, just continue
                        console.log(`Invalid selector: ${selector}`);
                    }
                }

                // If tab not found, try to find tab by text content
                if (!tabFound) {
                    const tabText = settingSection.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                    const tabs = document.querySelectorAll('.settings-tab, .tab-button, [role="tab"]');

                    for (const tab of tabs) {
                        if (tab.textContent.includes(tabText)) {
                            tab.click();
                            tabFound = true;
                            console.log(`Found settings tab by text content: ${tabText}`);
                            break;
                        }
                    }
                }

                // Scroll to the setting section
                setTimeout(() => {
                    this.scrollToSettingSection(settingId);
                }, tabFound ? 300 : 0);
            };

            // Helper method to scroll to setting section
            this.scrollToSettingSection = (settingId) => {
                console.log(`Scrolling to setting section: ${settingId}`);

                // Special handling for specific settings
                switch (settingId) {
                    case 'notifications':
                        const notificationsSection = document.getElementById('notificationsSettings') ||
                                                   document.querySelector('.notifications-settings') ||
                                                   document.querySelector('[data-setting="notifications"]') ||
                                                   document.querySelector('h3:contains("Notifications")');

                        if (notificationsSection) {
                            console.log('Found notifications section, scrolling to it');
                            notificationsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                            // Highlight the section
                            notificationsSection.classList.add('highlight-pulse');
                            setTimeout(() => {
                                notificationsSection.classList.remove('highlight-pulse');
                            }, 2000);
                            return;
                        }
                        break;

                    case 'theme':
                        const themeSection = document.getElementById('themeSettings') ||
                                           document.querySelector('.theme-settings') ||
                                           document.querySelector('[data-setting="theme"]') ||
                                           document.querySelector('h3:contains("Theme")');

                        if (themeSection) {
                            console.log('Found theme section, scrolling to it');
                            themeSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                            // Highlight the section
                            themeSection.classList.add('highlight-pulse');
                            setTimeout(() => {
                                themeSection.classList.remove('highlight-pulse');
                            }, 2000);
                            return;
                        }
                        break;

                    case 'privacy':
                        const privacySection = document.getElementById('privacySettings') ||
                                             document.querySelector('.privacy-settings') ||
                                             document.querySelector('[data-setting="privacy"]') ||
                                             document.querySelector('h3:contains("Privacy")');

                        if (privacySection) {
                            console.log('Found privacy section, scrolling to it');
                            privacySection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                            // Highlight the section
                            privacySection.classList.add('highlight-pulse');
                            setTimeout(() => {
                                privacySection.classList.remove('highlight-pulse');
                            }, 2000);
                            return;
                        }
                        break;
                }

                // Try multiple possible selectors for the setting section
                const selectors = [
                    `#${settingId}`,
                    `#${settingId}Settings`,
                    `[data-setting="${settingId}"]`,
                    `[data-setting-id="${settingId}"]`,
                    `.setting-section[data-section="${settingId}"]`,
                    `section[data-id="${settingId}"]`,
                    `div[data-feature="${settingId}"]`,
                    `.setting-card[data-id="${settingId}"]`,
                    `div[id*="${settingId}"]`,
                    `section[id*="${settingId}"]`,
                    `div[class*="${settingId}"]`
                ];

                // Try each selector
                let settingElement = null;
                for (const selector of selectors) {
                    try {
                        const element = document.querySelector(selector);
                        if (element) {
                            settingElement = element;
                            console.log(`Found setting element with selector: ${selector}`);
                            break;
                        }
                    } catch (e) {
                        // Some selectors might be invalid, just continue
                        console.log(`Invalid selector: ${selector}`);
                    }
                }

                // If still not found, try to find by text content
                if (!settingElement) {
                    const titleText = settingId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                    const headings = document.querySelectorAll('h2, h3, h4, .card-title, .section-title, .setting-title, .settings-section-title');

                    for (const heading of headings) {
                        if (heading.textContent.includes(titleText)) {
                            settingElement = heading.closest('section') || heading.closest('div') || heading;
                            console.log(`Found setting element by text content: ${titleText}`);
                            break;
                        }
                    }
                }

                // If found, scroll to it
                if (settingElement) {
                    settingElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    // Highlight the section
                    settingElement.classList.add('highlight-pulse');
                    setTimeout(() => {
                        settingElement.classList.remove('highlight-pulse');
                    }, 2000);
                } else {
                    console.log(`Setting section not found: ${settingId}`);
                    // If section not found, at least try to navigate to settings content area
                    const settingsArea = document.querySelector('.settings-content') ||
                                       document.querySelector('.settings-container') ||
                                       document.querySelector('#settingsContent');

                    if (settingsArea) {
                        settingsArea.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }
                }
            };

            // Add event listeners to profile results
            document.querySelectorAll('.profile-result').forEach(element => {
                element.addEventListener('click', () => {
                    const profileId = element.getAttribute('data-profile-id');

                    if (profileId) {
                        console.log(`Profile result clicked: ${profileId}`);

                        // Hide search results first
                        document.getElementById('globalSearchResults').classList.add('hidden');

                        // Switch to profile view
                        this.switchView('profile');

                        // Scroll to the profile section
                        setTimeout(() => {
                            this.scrollToProfileSection(profileId);
                        }, 300);
                    }
                });
            });

            // Helper method to scroll to profile section
            this.scrollToProfileSection = (profileId) => {
                console.log(`Scrolling to profile section: ${profileId}`);

                // Try multiple possible selectors for the profile section
                const selectors = [
                    `#${profileId}`,
                    `[data-profile="${profileId}"]`,
                    `[data-profile-section="${profileId}"]`,
                    `.profile-section[data-section="${profileId}"]`,
                    `section[data-id="${profileId}"]`,
                    `div[data-feature="${profileId}"]`,
                    `.profile-card[data-id="${profileId}"]`
                ];

                // Try each selector
                let profileElement = null;
                for (const selector of selectors) {
                    try {
                        const element = document.querySelector(selector);
                        if (element) {
                            profileElement = element;
                            console.log(`Found profile element with selector: ${selector}`);
                            break;
                        }
                    } catch (e) {
                        // Some selectors might be invalid, just continue
                        console.log(`Invalid selector: ${selector}`);
                    }
                }

                // If still not found, try to find by text content
                if (!profileElement) {
                    const titleText = profileId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                    const headings = document.querySelectorAll('h2, h3, h4, .card-title, .section-title');

                    for (const heading of headings) {
                        if (heading.textContent.includes(titleText)) {
                            profileElement = heading.closest('section') || heading.closest('div') || heading;
                            console.log(`Found profile element by text content: ${titleText}`);
                            break;
                        }
                    }
                }

                // If found, scroll to it
                if (profileElement) {
                    profileElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    profileElement.classList.add('highlight-pulse');
                    setTimeout(() => {
                        profileElement.classList.remove('highlight-pulse');
                    }, 2000);
                } else {
                    console.log(`Profile section not found: ${profileId}`);
                    // If section not found, at least try to navigate to profile area
                    const profileArea = document.querySelector('.profile-area') ||
                                      document.querySelector('.profile-container') ||
                                      document.querySelector('#profileContent');

                    if (profileArea) {
                        profileArea.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }
                }
            };

            // Add event listeners to admin results
            document.querySelectorAll('.admin-result').forEach(element => {
                element.addEventListener('click', () => {
                    const adminId = element.getAttribute('data-admin-id');

                    if (adminId) {
                        console.log(`Admin result clicked: ${adminId}`);

                        // Hide search results first
                        document.getElementById('globalSearchResults').classList.add('hidden');

                        // Navigate to the admin section
                        this.navigateToAdminSection(adminId);
                    }
                });
            });

            // Helper method to navigate to admin sections
            this.navigateToAdminSection = (adminId) => {
                console.log(`Navigating to admin section: ${adminId}`);

                // Special handling for specific admin sections
                switch (adminId) {
                    case 'system-logs':
                        console.log('Direct navigation to system logs modal');

                        // Try to find the logs modal
                        const logsModal = document.getElementById('logsModal');

                        if (logsModal) {
                            // Show the logs modal
                            logsModal.classList.remove('hidden');

                            // Initialize Lucide icons in the modal
                            if (window.lucide) {
                                lucide.createIcons({
                                    attrs: {
                                        class: ["h-5", "w-5"]
                                    },
                                    elements: [logsModal]
                                });
                            }

                            // Refresh logs if there's a refresh button
                            const refreshLogsBtn = document.getElementById('refreshLogs');
                            if (refreshLogsBtn) {
                                refreshLogsBtn.click();
                            }

                            return;
                        }
                        break;

                    case 'user-management':
                        // Try to find user management section directly
                        const userManagementSection = document.getElementById('userManagementSection') ||
                                                     document.querySelector('.user-management-section') ||
                                                     document.querySelector('[data-section="user-management"]');

                        if (userManagementSection) {
                            // If we have a dedicated user management section, go to it directly
                            this.switchView('admin');
                            setTimeout(() => {
                                userManagementSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                            }, 300);
                            return;
                        }
                        break;

                    case 'service-restrictions':
                        // Try to find service restrictions section directly
                        const restrictionsSection = document.getElementById('serviceRestrictionsSection') ||
                                                  document.querySelector('.service-restrictions-section') ||
                                                  document.querySelector('[data-section="service-restrictions"]');

                        if (restrictionsSection) {
                            // If we have a dedicated restrictions section, go to it directly
                            this.switchView('admin');
                            setTimeout(() => {
                                restrictionsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                            }, 300);
                            return;
                        }
                        break;

                    case 'active-users':
                        // Try to find active users section directly
                        const activeUsersSection = document.getElementById('activeUsersSection') ||
                                                 document.querySelector('.active-users-section') ||
                                                 document.querySelector('[data-section="active-users"]');

                        if (activeUsersSection) {
                            // If we have a dedicated active users section, go to it directly
                            this.switchView('admin');
                            setTimeout(() => {
                                activeUsersSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                            }, 300);
                            return;
                        }
                        break;

                    case 'system-settings':
                        // Try to find system settings section directly
                        const systemSettingsSection = document.getElementById('systemSettingsSection') ||
                                                    document.querySelector('.system-settings-section') ||
                                                    document.querySelector('[data-section="system-settings"]');

                        if (systemSettingsSection) {
                            // If we have a dedicated system settings section, go to it directly
                            this.switchView('admin');
                            setTimeout(() => {
                                systemSettingsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                            }, 300);
                            return;
                        }
                        break;
                }

                // First try to find if there's a dedicated admin page
                const adminPage = document.getElementById('adminPage') ||
                                 document.querySelector('.admin-page');

                // Try to find admin view
                const adminView = document.getElementById('adminView') ||
                                 document.querySelector('.admin-view') ||
                                 document.querySelector('[data-view="admin"]');

                if (adminView) {
                    // If there's a dedicated admin view, navigate to it
                    console.log('Switching to admin view');
                    this.switchView('admin');

                    // Then find the specific section
                    setTimeout(() => {
                        this.scrollToAdminSection(adminId);
                    }, 300);
                } else if (adminPage) {
                    // If there's a dedicated admin page, navigate to it
                    if (typeof this.showAdminPage === 'function') {
                        this.showAdminPage();
                    } else {
                        // Try to find and click admin nav link
                        const adminNavLink = document.querySelector('[data-nav="admin"]') ||
                                           document.querySelector('.nav-link[href="#admin"]');
                        if (adminNavLink) {
                            adminNavLink.click();
                        }
                    }

                    // Then find the specific section
                    setTimeout(() => {
                        this.scrollToAdminSection(adminId);
                    }, 300);
                } else {
                    // Otherwise, go to settings and select admin tab
                    this.switchView('settings');

                    // Look for admin tab with various possible IDs/selectors
                    const adminTab = document.getElementById('adminSettingsTab') ||
                                    document.getElementById('adminTab') ||
                                    document.querySelector('[data-tab="admin"]') ||
                                    document.querySelector('.settings-tab[data-section="admin"]') ||
                                    document.querySelector('[data-settings-tab="admin"]');

                    if (adminTab) {
                        console.log('Clicking admin tab');
                        adminTab.click();

                        // Scroll to the specific admin section
                        setTimeout(() => {
                            this.scrollToAdminSection(adminId);
                        }, 300);
                    } else {
                        console.log('Admin tab not found, trying direct navigation');
                        // If no admin tab, try to find the section directly
                        this.scrollToAdminSection(adminId);
                    }
                }
            };

            // Helper method to scroll to admin section
            this.scrollToAdminSection = (adminId) => {
                console.log(`Scrolling to admin section: ${adminId}`);

                // Special handling for system logs
                if (adminId === 'system-logs') {
                    console.log('Opening system logs modal');

                    // Try to find the logs modal
                    const logsModal = document.getElementById('logsModal');

                    if (logsModal) {
                        // Show the logs modal
                        logsModal.classList.remove('hidden');

                        // Initialize Lucide icons in the modal
                        if (window.lucide) {
                            lucide.createIcons({
                                attrs: {
                                    class: ["h-5", "w-5"]
                                },
                                elements: [logsModal]
                            });
                        }

                        // Refresh logs if there's a refresh button
                        const refreshLogsBtn = document.getElementById('refreshLogs');
                        if (refreshLogsBtn) {
                            refreshLogsBtn.click();
                        }

                        return;
                    }

                    // If modal not found, try to find logs section
                    const logsSection = document.getElementById('logsSection') ||
                                       document.querySelector('.logs-section') ||
                                       document.querySelector('[data-section="logs"]') ||
                                       document.querySelector('#adminLogs') ||
                                       document.querySelector('.admin-logs');

                    if (logsSection) {
                        console.log('Found logs section, scrolling to it');
                        logsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                        return;
                    }

                    // If logs tab exists, click it first
                    const logsTab = document.querySelector('[data-tab="logs"]') ||
                                   document.getElementById('logsTab') ||
                                   document.querySelector('.tab-item[data-section="logs"]');

                    if (logsTab) {
                        console.log('Found logs tab, clicking it');
                        logsTab.click();
                        setTimeout(() => {
                            const logsContent = document.querySelector('.logs-content') ||
                                              document.getElementById('logsContent') ||
                                              document.querySelector('[data-content="logs"]');
                            if (logsContent) {
                                logsContent.scrollIntoView({ behavior: 'smooth', block: 'start' });
                            }
                        }, 300);
                        return;
                    }
                }

                // Try multiple possible selectors for the admin section
                const selectors = [
                    `#${adminId}`,
                    `[data-admin="${adminId}"]`,
                    `[data-admin-section="${adminId}"]`,
                    `.admin-section[data-section="${adminId}"]`,
                    `section[data-id="${adminId}"]`,
                    `div[data-feature="${adminId}"]`,
                    `.admin-card[data-id="${adminId}"]`,
                    `[data-section="${adminId}"]`,
                    `#admin-${adminId}`,
                    `.${adminId}-section`
                ];

                // Try each selector
                let adminElement = null;
                for (const selector of selectors) {
                    try {
                        const element = document.querySelector(selector);
                        if (element) {
                            adminElement = element;
                            console.log(`Found admin element with selector: ${selector}`);
                            break;
                        }
                    } catch (e) {
                        // Some selectors might be invalid, just continue
                        console.log(`Invalid selector: ${selector}`);
                    }
                }

                // If still not found, try to find by text content
                if (!adminElement) {
                    const titleText = adminId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                    const headings = document.querySelectorAll('h2, h3, h4, .card-title, .section-title, .section-header');

                    for (const heading of headings) {
                        if (heading.textContent.includes(titleText)) {
                            adminElement = heading.closest('section') || heading.closest('div') || heading;
                            console.log(`Found admin element by text content: ${titleText}`);
                            break;
                        }
                    }
                }

                // If found, scroll to it
                if (adminElement) {
                    adminElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    adminElement.classList.add('highlight-pulse');
                    setTimeout(() => {
                        adminElement.classList.remove('highlight-pulse');
                    }, 2000);
                } else {
                    console.log(`Admin section not found: ${adminId}`);
                    // If section not found, at least try to navigate to admin area
                    const adminArea = document.querySelector('.admin-area') ||
                                     document.querySelector('.admin-container') ||
                                     document.querySelector('#adminContent');

                    if (adminArea) {
                        adminArea.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }
                }
            };
        }

        // Initialize Lucide icons
        if (window.lucide) {
            lucide.createIcons({
                attrs: {
                    class: ["h-4", "w-4"]
                },
                elements: [searchContent]
            });
        }
    }

    /**
     * Handle window resize events
     */
    handleWindowResize() {
        // Only adjust UI if the size change is significant (more than 50px)
        if (Math.abs(window.innerWidth - this.windowWidth) > 50 ||
            Math.abs(window.innerHeight - this.windowHeight) > 50) {

            // Update stored window dimensions
            this.windowWidth = window.innerWidth;
            this.windowHeight = window.innerHeight;

            // Adjust UI elements based on new screen size
            this.adjustUIForScreenSize();
        }
    }

    /**
     * Adjust UI elements based on current screen size
     */
    adjustUIForScreenSize() {
        const isMobile = window.innerWidth <= 640;
        const isTablet = window.innerWidth > 640 && window.innerWidth <= 1024;
        const isLandscape = window.innerWidth > window.innerHeight;

        // Adjust service cards grid
        this.adjustServiceCardsGrid(isMobile, isTablet);

        // Adjust modal sizes
        this.adjustModalSizes(isMobile, isTablet);

        // Adjust settings panels
        this.adjustSettingsPanels(isMobile, isTablet);

        // Handle landscape orientation on small screens
        if (isLandscape && window.innerHeight <= 500) {
            this.adjustForLandscapeOrientation();
        }

        // Ensure sidebar is properly sized
        this.adjustSidebar(isMobile, isTablet);

        // Ensure friends panel is properly sized
        this.adjustFriendsPanel(isMobile);

        // Ensure admin panels are properly sized
        if (this.isUserAdmin) {
            this.adjustAdminPanels(isMobile);
        }
    }

    /**
     * Adjust service cards grid based on screen size
     * @param {boolean} isMobile Whether the screen is mobile-sized
     * @param {boolean} isTablet Whether the screen is tablet-sized
     */
    adjustServiceCardsGrid(isMobile, isTablet) {
        const servicesContainer = document.getElementById('servicesContainer');
        if (!servicesContainer) return;

        // Adjust grid columns based on screen size
        if (isMobile) {
            servicesContainer.className = 'grid grid-cols-1 gap-4 mb-6';
        } else if (isTablet) {
            servicesContainer.className = 'grid grid-cols-2 gap-4 sm:gap-6 mb-6 sm:mb-8';
        } else if (window.innerWidth <= 1280) {
            servicesContainer.className = 'grid grid-cols-3 gap-4 sm:gap-6 mb-6 sm:mb-8';
        } else if (window.innerWidth <= 1536) {
            servicesContainer.className = 'grid grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8';
        } else {
            servicesContainer.className = 'grid grid-cols-5 gap-4 sm:gap-6 mb-6 sm:mb-8';
        }
    }

    /**
     * Adjust modal sizes based on screen size
     * @param {boolean} isMobile Whether the screen is mobile-sized
     * @param {boolean} isTablet Whether the screen is tablet-sized
     */
    adjustModalSizes(isMobile, isTablet) {
        // Find all modal content containers
        const modalContents = document.querySelectorAll('.modal-content, .bg-slate-800');

        modalContents.forEach(content => {
            if (isMobile) {
                content.style.width = '95%';
                content.style.maxHeight = '80vh';
            } else if (isTablet) {
                content.style.width = '90%';
                content.style.maxHeight = '85vh';
            } else {
                content.style.width = '';
                content.style.maxHeight = '';
            }
        });
    }

    /**
     * Adjust settings panels based on screen size
     * @param {boolean} isMobile Whether the screen is mobile-sized
     * @param {boolean} isTablet Whether the screen is tablet-sized
     */
    adjustSettingsPanels(isMobile, isTablet) {
        // Make settings tabs scrollable on mobile
        const settingsTabs = document.querySelector('.settings-tabs');
        if (settingsTabs) {
            if (isMobile) {
                settingsTabs.style.overflowX = 'auto';
                settingsTabs.style.whiteSpace = 'nowrap';
            } else {
                settingsTabs.style.overflowX = '';
                settingsTabs.style.whiteSpace = '';
            }
        }

        // Ensure settings sections don't overflow
        const settingsSections = document.querySelectorAll('.settings-section');
        settingsSections.forEach(section => {
            if (isMobile || isTablet) {
                section.style.overflowX = 'hidden';
                section.style.maxWidth = '100%';
            } else {
                section.style.overflowX = '';
                section.style.maxWidth = '';
            }
        });
    }

    /**
     * Adjust for landscape orientation on small screens
     */
    adjustForLandscapeOrientation() {
        // Adjust service cards for landscape
        const serviceCards = document.querySelectorAll('.service-card');
        serviceCards.forEach(card => {
            card.style.height = 'auto';
            card.style.minHeight = '180px';
        });

        // Adjust modal content for landscape
        const modalContents = document.querySelectorAll('.modal-content');
        modalContents.forEach(content => {
            content.style.maxHeight = '90vh';
        });

        // Adjust panels for landscape
        const panels = document.querySelectorAll('.settings-section, .admin-section, .panel-content');
        panels.forEach(panel => {
            panel.style.maxHeight = '80vh';
        });
    }

    /**
     * Adjust sidebar based on screen size
     * @param {boolean} isMobile Whether the screen is mobile-sized
     * @param {boolean} isTablet Whether the screen is tablet-sized
     */
    adjustSidebar(isMobile, isTablet) {
        const sidebar = document.querySelector('.sidebar-container');
        if (!sidebar) return;

        if (isMobile || isTablet) {
            // Ensure sidebar is properly set up for mobile
            sidebar.style.maxHeight = '100vh';
            sidebar.style.overflowY = 'auto';

            // Set up mobile menu toggle if not already set up
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const sidebarOverlay = document.querySelector('.sidebar-overlay');

            if (mobileMenuToggle && !mobileMenuToggle._hasEventListener) {
                mobileMenuToggle.addEventListener('click', (e) => {
                    // Stop event propagation to prevent document click handler from firing
                    e.stopPropagation();

                    // Remove the hidden class to make the sidebar visible on mobile
                    sidebar.classList.remove('hidden');

                    // Force any existing blur effects to be removed
                    sidebar.style.backdropFilter = 'none';

                    // Add the open class to slide it in
                    sidebar.classList.add('open');

                    // Remove the overlay completely - don't use it at all
                    if (sidebarOverlay) {
                        // Make sure it has the hidden class and remove active class
                        sidebarOverlay.classList.add('hidden');
                        sidebarOverlay.classList.remove('active');
                    }
                });
                mobileMenuToggle._hasEventListener = true;
            }

            // Set up document click handler to close sidebar when clicking outside
            if (!document._hasSidebarClickListener) {
                document.addEventListener('click', (e) => {
                    // Only handle clicks when the sidebar is open
                    if (sidebar.classList.contains('open')) {
                        // Check if the click is outside the sidebar and not on the menu toggle
                        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
                        if (!sidebar.contains(e.target) && (!mobileMenuToggle || !mobileMenuToggle.contains(e.target))) {
                            // Close the sidebar
                            sidebar.classList.remove('open');

                            // Add a small delay before adding the hidden class back (for larger screens)
                            // This ensures the animation completes before hiding
                            setTimeout(() => {
                                if (window.innerWidth > 768) {
                                    sidebar.classList.add('hidden');
                                }
                            }, 300);
                        }
                    }
                });
                document._hasSidebarClickListener = true;
            }
        } else {
            // Reset sidebar styles for desktop
            sidebar.style.maxHeight = '';
            sidebar.style.overflowY = '';
            sidebar.classList.remove('open');

            const sidebarOverlay = document.querySelector('.sidebar-overlay');
            if (sidebarOverlay) {
                sidebarOverlay.classList.remove('active');
            }
        }
    }

    /**
     * Adjust friends panel based on screen size
     * @param {boolean} isMobile Whether the screen is mobile-sized
     */
    adjustFriendsPanel(isMobile) {
        const friendListPanel = document.getElementById('friendListPanel');
        const friendChatPanel = document.getElementById('friendChatPanel');
        const messagesContainer = document.getElementById('messagesContainer');

        if (friendListPanel) {
            if (isMobile) {
                friendListPanel.style.maxHeight = '70vh';
                friendListPanel.style.overflowY = 'auto';
            } else {
                friendListPanel.style.maxHeight = '';
                friendListPanel.style.overflowY = '';
            }
        }

        if (friendChatPanel) {
            if (isMobile) {
                friendChatPanel.style.maxHeight = '70vh';
                friendChatPanel.style.overflowY = 'auto';
            } else {
                friendChatPanel.style.maxHeight = '';
                friendChatPanel.style.overflowY = '';
            }
        }

        if (messagesContainer) {
            if (isMobile) {
                messagesContainer.style.maxHeight = '50vh';
            } else {
                messagesContainer.style.maxHeight = '';
            }
        }
    }

    /**
     * Adjust admin panels based on screen size
     * @param {boolean} isMobile Whether the screen is mobile-sized
     */
    adjustAdminPanels(isMobile) {
        const adminPanels = document.querySelectorAll('.admin-panel, .admin-section');

        adminPanels.forEach(panel => {
            if (isMobile) {
                panel.style.overflowX = 'auto';
            } else {
                panel.style.overflowX = '';
            }
        });

        // Make tables responsive
        const tables = document.querySelectorAll('table');
        tables.forEach(table => {
            if (isMobile) {
                table.style.display = 'block';
                table.style.overflowX = 'auto';
                table.style.whiteSpace = 'nowrap';
            } else {
                table.style.display = '';
                table.style.overflowX = '';
                table.style.whiteSpace = '';
            }
        });
    }

    /**
     * Set up connected services section
     */
    async setupConnectedServices() {
        try {
            const connectedServicesContainer = document.getElementById('connectedServicesContainer');
            const noConnectedServicesMessage = document.getElementById('noConnectedServices');

            if (!connectedServicesContainer || !noConnectedServicesMessage) {
                console.error('Connected services elements not found');
                return;
            }

            // Get connected services
            const services = await window.settingsManager.getConnectedServices();

            // Clear loading state
            connectedServicesContainer.innerHTML = '';

            if (services.length === 0) {
                // Show no connected services message
                noConnectedServicesMessage.classList.remove('hidden');
                return;
            }

            // Hide no connected services message
            noConnectedServicesMessage.classList.add('hidden');

            // Add each connected service
            services.forEach(service => {
                const serviceCard = document.createElement('div');
                serviceCard.className = 'bg-slate-700/30 p-3 rounded-lg';
                serviceCard.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="p-2 rounded-lg bg-${service.color}-500/10">
                                <i data-lucide="${service.icon}" class="h-4 w-4 text-${service.color}-500"></i>
                            </div>
                            <div>
                                <div class="text-sm font-medium text-slate-200">${service.name}</div>
                                <div class="text-xs text-slate-400">Connected: ${this.formatDate(service.connected_at)}</div>
                            </div>
                        </div>
                        <button class="disconnect-service-btn text-xs bg-red-900/50 hover:bg-red-900 text-red-200 px-2 py-1 rounded flex items-center" data-service-id="${service.id}">
                            <i data-lucide="unlink" class="h-3 w-3 mr-1"></i>
                            Disconnect
                        </button>
                    </div>
                `;

                connectedServicesContainer.appendChild(serviceCard);

                // Add event listener to disconnect button
                const disconnectBtn = serviceCard.querySelector('.disconnect-service-btn');
                if (disconnectBtn) {
                    disconnectBtn.addEventListener('click', () => {
                        this.disconnectService(service.id, service.name);
                    });
                }
            });

            // Initialize icons
            if (window.lucide) {
                lucide.createIcons();
            }
        } catch (error) {
            console.error('Error setting up connected services:', error);

            // Show error message
            const connectedServicesContainer = document.getElementById('connectedServicesContainer');
            if (connectedServicesContainer) {
                connectedServicesContainer.innerHTML = `
                    <div class="bg-red-900/20 border border-red-700/50 rounded-lg p-3 text-center">
                        <p class="text-sm text-red-200">Failed to load connected services. Please try again later.</p>
                    </div>
                `;
            }
        }
    }

    /**
     * Format date for display
     * @param {string} dateString ISO date string
     * @returns {string} Formatted date
     */
    formatDate(dateString) {
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString(undefined, { year: 'numeric', month: 'short', day: 'numeric' });
        } catch (error) {
            return 'Unknown date';
        }
    }

    /**
     * Disconnect a service
     * @param {string} serviceId Service ID
     * @param {string} serviceName Service name
     */
    async disconnectService(serviceId, serviceName) {
        // Show confirmation dialog
        if (!confirm(`Are you sure you want to disconnect ${serviceName}? You will need to reconnect to use this service again.`)) {
            return;
        }

        try {
            // Show loading state
            const disconnectBtn = document.querySelector(`.disconnect-service-btn[data-service-id="${serviceId}"]`);
            if (disconnectBtn) {
                disconnectBtn.innerHTML = `
                    <i data-lucide="loader" class="h-3 w-3 mr-1 animate-spin"></i>
                    Disconnecting...
                `;
                disconnectBtn.disabled = true;

                // Initialize icons
                if (window.lucide) {
                    lucide.createIcons();
                }
            }

            // Disconnect service
            const success = await window.settingsManager.disconnectService(serviceId);

            if (success) {
                // Refresh connected services
                this.setupConnectedServices();

                // Show success message
                this.showToast(`${serviceName} disconnected successfully`);
            } else {
                // Show error message
                this.showToast(`Failed to disconnect ${serviceName}`, 'error');

                // Reset button
                if (disconnectBtn) {
                    disconnectBtn.innerHTML = `
                        <i data-lucide="unlink" class="h-3 w-3 mr-1"></i>
                        Disconnect
                    `;
                    disconnectBtn.disabled = false;

                    // Initialize icons
                    if (window.lucide) {
                        lucide.createIcons();
                    }
                }
            }
        } catch (error) {
            console.error(`Error disconnecting service ${serviceId}:`, error);

            // Show error message
            this.showToast(`Failed to disconnect ${serviceName}: ${error.message}`, 'error');

            // Reset button
            const disconnectBtn = document.querySelector(`.disconnect-service-btn[data-service-id="${serviceId}"]`);
            if (disconnectBtn) {
                disconnectBtn.innerHTML = `
                    <i data-lucide="unlink" class="h-3 w-3 mr-1"></i>
                    Disconnect
                `;
                disconnectBtn.disabled = false;

                // Initialize icons
                if (window.lucide) {
                    lucide.createIcons();
                }
            }
        }
    }

    /**
     * Load current settings
     */
    loadCurrentSettings() {
        const settings = window.settingsManager.getAllSettings();
        console.log('Loaded settings:', settings);

        // Apply settings to UI
        this.applySettingsToUI(settings);
    }

    /**
     * Apply settings to UI
     * @param {Object} settings Settings object
     */
    applySettingsToUI(settings) {
        // Apply appearance settings
        const { appearance } = settings;
        if (appearance) {
            // Dark mode
            const darkModeToggle = document.getElementById('darkModeToggle');
            if (darkModeToggle) {
                darkModeToggle.checked = appearance.darkMode;
            }

            // Accent color
            const accentColorBtns = document.querySelectorAll('.accent-color-btn');
            accentColorBtns.forEach(btn => {
                const color = btn.getAttribute('data-color');
                if (color === appearance.accentColor) {
                    btn.classList.add('ring-2');
                    btn.classList.add(`ring-${color}-500`);
                } else {
                    btn.classList.remove('ring-2');
                    btn.classList.remove(`ring-${color}-500`);
                }
            });

            // Font size
            const fontSizeBtns = document.querySelectorAll('.font-size-btn');
            fontSizeBtns.forEach(btn => {
                const size = btn.getAttribute('data-font-size');
                if (size === appearance.fontSize) {
                    btn.classList.add('bg-cyan-600');
                    btn.classList.add('text-white');
                    btn.classList.remove('bg-slate-700');
                    btn.classList.remove('text-slate-300');
                } else {
                    btn.classList.remove('bg-cyan-600');
                    btn.classList.remove('text-white');
                    btn.classList.add('bg-slate-700');
                    btn.classList.add('text-slate-300');
                }
            });
        }

        // Apply notification settings
        const { notifications } = settings;
        if (notifications) {
            // Enabled
            const notificationsEnabledToggle = document.getElementById('notificationsEnabledToggle');
            if (notificationsEnabledToggle) {
                notificationsEnabledToggle.checked = notifications.enabled;
            }

            // Sound
            const notificationsSoundToggle = document.getElementById('notificationsSoundToggle');
            if (notificationsSoundToggle) {
                notificationsSoundToggle.checked = notifications.sound;
            }

            // Desktop
            const notificationsDesktopToggle = document.getElementById('notificationsDesktopToggle');
            if (notificationsDesktopToggle) {
                notificationsDesktopToggle.checked = notifications.desktop;
            }

            // Updates
            const notificationsUpdatesToggle = document.getElementById('notificationsUpdatesToggle');
            if (notificationsUpdatesToggle) {
                notificationsUpdatesToggle.checked = notifications.updates;
            }

            // Services
            const notificationsServicesToggle = document.getElementById('notificationsServicesToggle');
            if (notificationsServicesToggle) {
                notificationsServicesToggle.checked = notifications.services;
            }
        }

        // Apply privacy settings
        const { privacy } = settings;
        if (privacy) {
            // Show online status
            const privacyOnlineStatusToggle = document.getElementById('privacyOnlineStatusToggle');
            if (privacyOnlineStatusToggle) {
                privacyOnlineStatusToggle.checked = privacy.showOnlineStatus;
            }

            // Share activity
            const privacyShareActivityToggle = document.getElementById('privacyShareActivityToggle');
            if (privacyShareActivityToggle) {
                privacyShareActivityToggle.checked = privacy.shareActivity;
            }

            // Allow friend requests
            const privacyFriendRequestsToggle = document.getElementById('privacyFriendRequestsToggle');
            if (privacyFriendRequestsToggle) {
                privacyFriendRequestsToggle.checked = privacy.allowFriendRequests;
            }
        }

        // Apply account settings
        const { account } = settings;
        if (account) {
            // Username
            const accountUsername = document.getElementById('accountUsername');
            if (accountUsername) {
                accountUsername.value = account.username;
            }

            // Email
            const accountEmail = document.getElementById('accountEmail');
            if (accountEmail) {
                accountEmail.value = account.email;
            }


        }

        // Service visibility is now managed through the Store
    }

    /**
     * Set up appearance settings
     */
    setupAppearanceSettings() {
        // Dark mode toggle
        const darkModeToggle = document.getElementById('darkModeToggle');
        if (darkModeToggle) {
            darkModeToggle.addEventListener('change', () => {
                window.settingsManager.updateSetting('appearance', 'darkMode', darkModeToggle.checked);
                this.applyDarkMode(darkModeToggle.checked);
            });
        }

        // Accent color buttons
        const accentColorBtns = document.querySelectorAll('.accent-color-btn');
        accentColorBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const color = btn.getAttribute('data-color');
                window.settingsManager.updateSetting('appearance', 'accentColor', color);

                // Update UI
                accentColorBtns.forEach(b => {
                    b.classList.remove('ring-2');
                    b.classList.remove('ring-cyan-500');
                    b.classList.remove('ring-purple-500');
                    b.classList.remove('ring-green-500');
                    b.classList.remove('ring-amber-500');
                    b.classList.remove('ring-red-500');
                    b.classList.remove('ring-blue-500');
                });

                btn.classList.add('ring-2');
                btn.classList.add(`ring-${color}-500`);

                // Apply accent color
                this.applyAccentColor(color);
            });
        });

        // Font size buttons
        const fontSizeBtns = document.querySelectorAll('.font-size-btn');
        fontSizeBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const size = btn.getAttribute('data-font-size');
                window.settingsManager.updateSetting('appearance', 'fontSize', size);

                // Update UI
                fontSizeBtns.forEach(b => {
                    b.classList.remove('bg-cyan-600');
                    b.classList.remove('text-white');
                    b.classList.add('bg-slate-700');
                    b.classList.add('text-slate-300');
                });

                btn.classList.add('bg-cyan-600');
                btn.classList.add('text-white');
                btn.classList.remove('bg-slate-700');
                btn.classList.remove('text-slate-300');

                // Apply font size
                this.applyFontSize(size);
            });
        });
    }

    /**
     * Apply dark mode
     * @param {boolean} darkMode Whether dark mode is enabled
     */
    applyDarkMode(darkMode) {
        if (darkMode) {
            document.documentElement.classList.add('dark-mode');
        } else {
            document.documentElement.classList.remove('dark-mode');
        }
    }

    /**
     * Apply accent color
     * @param {string} color Accent color
     */
    applyAccentColor(color) {
        // Add CSS class for the accent color
        document.documentElement.classList.remove('accent-cyan', 'accent-purple', 'accent-green', 'accent-amber', 'accent-red', 'accent-blue');
        document.documentElement.classList.add(`accent-${color}`);
    }

    /**
     * Apply font size
     * @param {string} size Font size
     */
    applyFontSize(size) {
        // Add CSS class for the font size
        document.documentElement.classList.remove('font-small', 'font-medium', 'font-large');
        document.documentElement.classList.add(`font-${size}`);
    }

    /**
     * Set up notification settings
     */
    setupNotificationSettings() {
        // Enabled toggle
        const notificationsEnabledToggle = document.getElementById('notificationsEnabledToggle');
        if (notificationsEnabledToggle) {
            notificationsEnabledToggle.addEventListener('change', () => {
                window.settingsManager.updateSetting('notifications', 'enabled', notificationsEnabledToggle.checked);

                // Update dependent toggles
                const dependentToggles = [
                    document.getElementById('notificationsSoundToggle'),
                    document.getElementById('notificationsDesktopToggle'),
                    document.getElementById('notificationsUpdatesToggle'),
                    document.getElementById('notificationsServicesToggle')
                ];

                dependentToggles.forEach(toggle => {
                    if (toggle) {
                        toggle.disabled = !notificationsEnabledToggle.checked;
                        toggle.parentElement.classList.toggle('opacity-50', !notificationsEnabledToggle.checked);
                    }
                });
            });
        }

        // Sound toggle
        const notificationsSoundToggle = document.getElementById('notificationsSoundToggle');
        if (notificationsSoundToggle) {
            notificationsSoundToggle.addEventListener('change', () => {
                window.settingsManager.updateSetting('notifications', 'sound', notificationsSoundToggle.checked);
            });
        }

        // Desktop toggle
        const notificationsDesktopToggle = document.getElementById('notificationsDesktopToggle');
        if (notificationsDesktopToggle) {
            notificationsDesktopToggle.addEventListener('change', () => {
                window.settingsManager.updateSetting('notifications', 'desktop', notificationsDesktopToggle.checked);

                // Request notification permission if enabled
                if (notificationsDesktopToggle.checked && Notification.permission !== 'granted' && Notification.permission !== 'denied') {
                    Notification.requestPermission();
                }
            });
        }

        // Updates toggle
        const notificationsUpdatesToggle = document.getElementById('notificationsUpdatesToggle');
        if (notificationsUpdatesToggle) {
            notificationsUpdatesToggle.addEventListener('change', () => {
                window.settingsManager.updateSetting('notifications', 'updates', notificationsUpdatesToggle.checked);
            });
        }

        // Services toggle
        const notificationsServicesToggle = document.getElementById('notificationsServicesToggle');
        if (notificationsServicesToggle) {
            notificationsServicesToggle.addEventListener('change', () => {
                window.settingsManager.updateSetting('notifications', 'services', notificationsServicesToggle.checked);
            });
        }
    }

    /**
     * Set up privacy settings
     */
    setupPrivacySettings() {
        // Show online status toggle
        const privacyOnlineStatusToggle = document.getElementById('privacyOnlineStatusToggle');
        if (privacyOnlineStatusToggle) {
            privacyOnlineStatusToggle.addEventListener('change', () => {
                window.settingsManager.updateSetting('privacy', 'showOnlineStatus', privacyOnlineStatusToggle.checked);
            });
        }

        // Share activity toggle
        const privacyShareActivityToggle = document.getElementById('privacyShareActivityToggle');
        if (privacyShareActivityToggle) {
            privacyShareActivityToggle.addEventListener('change', () => {
                window.settingsManager.updateSetting('privacy', 'shareActivity', privacyShareActivityToggle.checked);
            });
        }

        // Allow friend requests toggle
        const privacyFriendRequestsToggle = document.getElementById('privacyFriendRequestsToggle');
        if (privacyFriendRequestsToggle) {
            privacyFriendRequestsToggle.addEventListener('change', () => {
                window.settingsManager.updateSetting('privacy', 'allowFriendRequests', privacyFriendRequestsToggle.checked);
            });
        }
    }

    /**
     * Set up account settings
     */
    setupAccountSettings() {
        // Save login (username/email) button
        const saveLoginBtn = document.getElementById('saveLogin');
        if (saveLoginBtn) {
            saveLoginBtn.addEventListener('click', () => {
                const login = document.getElementById('accountLogin').value;
                if (login) {
                    window.settingsManager.updateSetting('account', 'login', login);
                    this.showToast('Account details saved successfully');
                }
            });
        }

        // Change password button
        const changePasswordBtn = document.getElementById('changePasswordBtn');
        if (changePasswordBtn) {
            changePasswordBtn.addEventListener('click', () => {
                this.showChangePasswordModal(false); // Start with reset password flow
            });
        }


    }

    /**
     * Show change password modal
     * @param {boolean} knowsPassword - Whether the user knows their current password
     */
    showChangePasswordModal(knowsPassword = true) {
        // Create modal
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black/70 flex items-center justify-center z-50';
        modal.id = 'changePasswordModal';

        // Step 1: Enter username/email and password (if known)
        modal.innerHTML = `
            <div class="bg-slate-800 rounded-lg p-6 w-96 max-w-full">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-slate-100">${knowsPassword ? 'Change Password' : 'Reset Password'}</h3>
                    <button id="closePasswordModal" class="text-slate-400 hover:text-slate-100">
                        <i data-lucide="x" class="h-5 w-5"></i>
                    </button>
                </div>
                <div id="passwordStep1" class="space-y-4">
                    <p class="text-slate-300 text-sm">${knowsPassword ? 'Enter your username/email and current password.' : 'Enter your username or email to receive a verification code.'}</p>
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-1">Username or Email</label>
                        <input type="text" id="passwordLogin" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500" required>
                    </div>
                    ${knowsPassword ? `
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-1">Current Password</label>
                        <input type="password" id="currentPassword" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500" required>
                    </div>` : ''}
                    <div class="pt-2">
                        <button type="button" id="requestVerificationBtn" class="w-full bg-cyan-600 hover:bg-cyan-500 text-white rounded-md px-4 py-2 flex items-center justify-center">
                            <i data-lucide="${knowsPassword ? 'key' : 'mail'}" class="h-4 w-4 mr-2"></i>
                            ${knowsPassword ? 'Verify & Continue' : 'Send Verification Code'}
                        </button>
                    </div>
                    <div class="text-center mt-2">
                        <button type="button" id="togglePasswordModeBtn" class="text-cyan-400 hover:text-cyan-300 text-sm">
                            ${knowsPassword ? 'Forgot your password?' : 'I know my password'}
                        </button>
                    </div>
                </div>
                <div id="passwordStep2" class="space-y-4 hidden">
                    <p class="text-slate-300 text-sm">Enter the verification code sent to your email and your new password.</p>
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-1">Verification Code</label>
                        <input type="text" id="verificationCode" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-1">New Password</label>
                        <input type="password" id="newPassword" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500" required>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-1">Confirm New Password</label>
                        <input type="password" id="confirmPassword" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500" required>
                    </div>
                    <div class="pt-2">
                        <button type="button" id="changePasswordBtn" class="w-full bg-cyan-600 hover:bg-cyan-500 text-white rounded-md px-4 py-2 flex items-center justify-center">
                            <i data-lucide="key" class="h-4 w-4 mr-2"></i>
                            ${knowsPassword ? 'Change Password' : 'Reset Password'}
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Refresh icons
        if (window.lucide) {
            lucide.createIcons();
        }

        // Add event listeners
        const closeButton = document.getElementById('closePasswordModal');
        if (closeButton) {
            closeButton.addEventListener('click', () => {
                modal.remove();
            });
        }

        // Toggle between password modes (knows password vs forgot password)
        const togglePasswordModeBtn = document.getElementById('togglePasswordModeBtn');
        if (togglePasswordModeBtn) {
            togglePasswordModeBtn.addEventListener('click', () => {
                modal.remove();
                this.showChangePasswordModal(!knowsPassword);
            });
        }

        // Store user data for step 2
        let userData = {
            login: '',
            email: ''
        };

        // Request verification button (Step 1)
        const requestVerificationBtn = document.getElementById('requestVerificationBtn');
        if (requestVerificationBtn) {
            requestVerificationBtn.addEventListener('click', () => {
                const login = document.getElementById('passwordLogin').value;
                if (!login) {
                    this.showToast('Please enter your username or email', 'error');
                    return;
                }

                // If knows password, also check current password
                let currentPassword = '';
                if (knowsPassword) {
                    currentPassword = document.getElementById('currentPassword').value;
                    if (!currentPassword) {
                        this.showToast('Please enter your current password', 'error');
                        return;
                    }
                }

                // Show loading state
                const originalButtonText = requestVerificationBtn.innerHTML;
                requestVerificationBtn.innerHTML = `<i data-lucide="loader" class="h-4 w-4 mr-2 animate-spin"></i>${knowsPassword ? 'Verifying...' : 'Sending Code...'}`;
                requestVerificationBtn.disabled = true;

                // Initialize icons
                if (window.lucide) {
                    lucide.createIcons({
                        attrs: {
                            class: ["h-4", "w-4"]
                        },
                        elements: [requestVerificationBtn]
                    });
                }

                // Call the appropriate API based on mode
                const apiEndpoint = knowsPassword ? '/auth/verify-credentials' : '/auth/request-password-reset';
                const requestBody = knowsPassword ?
                    { login, password: currentPassword } :
                    { login };

                fetch(apiEndpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        this.showToast(data.error, 'error');
                        requestVerificationBtn.innerHTML = originalButtonText;
                        requestVerificationBtn.disabled = false;
                        if (window.lucide) lucide.createIcons();
                    } else {
                        // Store user data for step 2
                        userData.login = login;
                        userData.email = data.email || login;

                        this.showToast(data.message || 'Verification code sent successfully');

                        // Show step 2
                        document.getElementById('passwordStep1').classList.add('hidden');
                        document.getElementById('passwordStep2').classList.remove('hidden');
                    }
                })
                .catch(error => {
                    console.error(`Error ${knowsPassword ? 'verifying credentials' : 'requesting password reset'}:`, error);
                    this.showToast(`Failed to ${knowsPassword ? 'verify credentials' : 'send verification code'}. Please try again.`, 'error');
                    requestVerificationBtn.innerHTML = originalButtonText;
                    requestVerificationBtn.disabled = false;
                    if (window.lucide) lucide.createIcons();
                });
            });
        }

        // Change/Reset Password button (Step 2)
        const changePasswordBtn = document.getElementById('changePasswordBtn');
        if (changePasswordBtn) {
            changePasswordBtn.addEventListener('click', () => {
                const verificationCode = document.getElementById('verificationCode').value;
                const newPassword = document.getElementById('newPassword').value;
                const confirmPassword = document.getElementById('confirmPassword').value;

                if (!verificationCode) {
                    this.showToast('Please enter the verification code', 'error');
                    return;
                }

                if (!newPassword) {
                    this.showToast('Please enter a new password', 'error');
                    return;
                }

                if (newPassword !== confirmPassword) {
                    this.showToast('New passwords do not match', 'error');
                    return;
                }

                // Show loading state
                const originalButtonText = changePasswordBtn.innerHTML;
                changePasswordBtn.innerHTML = `<i data-lucide="loader" class="h-4 w-4 mr-2 animate-spin"></i>${knowsPassword ? 'Changing' : 'Resetting'} Password...`;
                changePasswordBtn.disabled = true;

                // Initialize icons
                if (window.lucide) {
                    lucide.createIcons({
                        attrs: {
                            class: ["h-4", "w-4"]
                        },
                        elements: [changePasswordBtn]
                    });
                }

                // Call the API to reset password
                fetch('/auth/reset-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: userData.email,
                        verification_code: verificationCode,
                        new_password: newPassword
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        this.showToast(data.error, 'error');
                        changePasswordBtn.innerHTML = originalButtonText;
                        changePasswordBtn.disabled = false;
                        if (window.lucide) lucide.createIcons();
                    } else {
                        this.showToast(data.message || `Password ${knowsPassword ? 'changed' : 'reset'} successfully`);
                        modal.remove();
                    }
                })
                .catch(error => {
                    console.error(`Error ${knowsPassword ? 'changing' : 'resetting'} password:`, error);
                    this.showToast(`Failed to ${knowsPassword ? 'change' : 'reset'} password. Please try again.`, 'error');
                    changePasswordBtn.innerHTML = originalButtonText;
                    changePasswordBtn.disabled = false;
                    if (window.lucide) lucide.createIcons();
                });
            });
        }
    }

    /**
     * Show a toast notification
     * @param {string} message Toast message
     * @param {string} type Toast type (success, error, info)
     */
    showToast(message, type = 'success') {
        // Create toast container if it doesn't exist
        let toastContainer = document.getElementById('toastContainer');

        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toastContainer';
            toastContainer.className = 'fixed bottom-4 right-4 z-50 flex flex-col space-y-2';
            document.body.appendChild(toastContainer);
        }

        // Create toast
        const toast = document.createElement('div');
        toast.className = `p-3 rounded-lg shadow-lg flex items-center space-x-2 transition-all duration-300 transform translate-x-full opacity-0`;

        // Set toast color based on type
        if (type === 'success') {
            toast.classList.add('bg-green-600', 'text-white');
        } else if (type === 'error') {
            toast.classList.add('bg-red-600', 'text-white');
        } else {
            toast.classList.add('bg-slate-700', 'text-slate-200');
        }

        // Set toast content
        toast.innerHTML = `
            <i data-lucide="${type === 'success' ? 'check-circle' : (type === 'error' ? 'alert-circle' : 'info')}" class="h-5 w-5"></i>
            <span>${message}</span>
        `;

        // Add toast to container
        toastContainer.appendChild(toast);

        // Refresh icons
        if (window.lucide) {
            lucide.createIcons({
                attrs: {
                    class: ["h-5", "w-5"]
                },
                elements: [toast]
            });
        }

        // Animate toast in
        setTimeout(() => {
            toast.classList.remove('translate-x-full', 'opacity-0');
        }, 10);

        // Remove toast after delay
        setTimeout(() => {
            toast.classList.add('translate-x-full', 'opacity-0');

            setTimeout(() => {
                toast.remove();

                // Remove container if empty
                if (toastContainer.children.length === 0) {
                    toastContainer.remove();
                }
            }, 300);
        }, 3000);
    }

    /**
     * Set up admin mode toggle
     */
    setupAdminModeToggle() {
        const adminLink = document.getElementById('adminLink');

        if (adminLink) {
            // Check if user is an admin
            this.checkAdminStatus().then(adminStatus => {
                if (adminStatus.is_admin) {
                    // Show admin link for admin users
                    adminLink.classList.remove('hidden');

                    // Always enable admin mode for admin users
                    this.isAdminMode = true;
                } else {
                    // Keep admin link hidden for non-admin users
                    adminLink.classList.add('hidden');
                    this.isAdminMode = false;
                }

                // Update UI based on admin mode
                this.updateUIForAdminMode();

                // Initialize icons after DOM changes
                if (window.lucide) {
                    lucide.createIcons();
                }
            });

            // Add event listener for admin link
            adminLink.addEventListener('click', (e) => {
                e.preventDefault();
                // Close the user menu
                const userMenu = document.getElementById('userMenu');
                if (userMenu) {
                    // Animate out
                    userMenu.style.opacity = '0';
                    userMenu.style.transform = 'scale(0.95)';

                    // Hide after animation completes
                    setTimeout(() => {
                        userMenu.classList.add('hidden');
                        // Reset styles for next opening
                        userMenu.style.opacity = '';
                        userMenu.style.transform = '';
                    }, 200);
                }

                // Switch to admin view or functionality
                this.switchView('admin');
            });
        }

        // Set up settings tabs
        this.setupSettingsTabs();
    }

    /**
     * Set up settings tabs
     */
    setupSettingsTabs() {
        const tabs = document.querySelectorAll('.settings-tab');
        const tabPanes = document.querySelectorAll('.settings-tab-pane');

        // First, ensure all panes are hidden initially
        tabPanes.forEach(pane => {
            pane.classList.add('hidden');
        });

        // Make sure only the default tab is active
        const defaultTab = document.getElementById('userSettingsTab');
        const defaultPane = document.getElementById('userSettingsSection');

        if (defaultTab && defaultPane) {
            // Update tab styles - remove active state from all tabs
            tabs.forEach(t => {
                t.classList.remove('active', 'border-cyan-500', 'text-cyan-400');
                t.classList.add('border-transparent', 'text-slate-400');
            });

            // Set active state on default tab
            defaultTab.classList.add('active', 'border-cyan-500', 'text-cyan-400');
            defaultTab.classList.remove('border-transparent', 'text-slate-400');

            // Show only the default pane
            defaultPane.classList.remove('hidden');
        }

        // Set up tab click handlers
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabId = tab.id;
                let sectionId;

                // Determine which section to show based on tab ID
                if (tabId === 'userSettingsTab') {
                    sectionId = 'userSettingsSection';
                } else if (tabId === 'adminSettingsTab') {
                    sectionId = 'adminSettingsSection';
                } else if (tabId === 'appearanceSettingsTab') {
                    sectionId = 'appearanceSettingsSection';
                } else if (tabId === 'connectionsSettingsTab') {
                    sectionId = 'connectionsSettingsSection';
                }

                // Update tab styles
                tabs.forEach(t => {
                    t.classList.remove('active', 'border-cyan-500', 'text-cyan-400');
                    t.classList.add('border-transparent', 'text-slate-400');
                });

                tab.classList.add('active', 'border-cyan-500', 'text-cyan-400');
                tab.classList.remove('border-transparent', 'text-slate-400');

                // Hide all panes
                tabPanes.forEach(pane => {
                    pane.classList.add('hidden');
                });

                // Show only the selected section
                if (sectionId) {
                    const section = document.getElementById(sectionId);
                    if (section) {
                        section.classList.remove('hidden');
                    }
                }

                // Also fix all settings sections to ensure only the active tab's content is shown
                this.fixSettingsSections();

                console.log(`Switched to settings tab: ${tabId}`);
            });
        });

        // Fix settings sections on initialization
        this.fixSettingsSections();

        console.log('Settings tabs initialized');
    }

    /**
     * Reset settings tabs to default state
     * This ensures only the active tab's content is shown
     */
    resetSettingsTabs() {
        // Get all settings tabs and panes
        const tabs = document.querySelectorAll('.settings-tab');
        const tabPanes = document.querySelectorAll('.settings-tab-pane');

        // Always force reset to the first tab (userSettingsTab) to ensure consistent state
        const defaultTab = document.getElementById('userSettingsTab');
        const defaultPane = document.getElementById('userSettingsSection');

        if (defaultTab && defaultPane) {
            // Update tab styles - remove active state from all tabs
            tabs.forEach(t => {
                t.classList.remove('active', 'border-cyan-500', 'text-cyan-400');
                t.classList.add('border-transparent', 'text-slate-400');
            });

            // Set active state on default tab
            defaultTab.classList.add('active', 'border-cyan-500', 'text-cyan-400');
            defaultTab.classList.remove('border-transparent', 'text-slate-400');

            // Hide all panes
            tabPanes.forEach(pane => {
                pane.classList.add('hidden');
            });

            // Show only the default pane
            defaultPane.classList.remove('hidden');

            console.log('Settings tabs reset to default state');
        } else {
            console.error('Default settings tab or pane not found');
        }
    }

    /**
     * Fix settings sections visibility
     * This ensures only the sections for the active tab are shown
     */
    fixSettingsSections() {
        // Get all settings sections
        const userSettingsSection = document.getElementById('userSettingsSection');
        const adminSettingsSection = document.getElementById('adminSettingsSection');
        const appearanceSettingsSection = document.getElementById('appearanceSettingsSection');
        const connectionsSettingsSection = document.getElementById('connectionsSettingsSection');

        // Get all settings tabs
        const userSettingsTab = document.getElementById('userSettingsTab');
        const adminSettingsTab = document.getElementById('adminSettingsTab');
        const appearanceSettingsTab = document.getElementById('appearanceSettingsTab');
        const connectionsSettingsTab = document.getElementById('connectionsSettingsTab');

        // Find the active tab
        let activeTab = null;
        if (userSettingsTab && userSettingsTab.classList.contains('active')) {
            activeTab = 'user';
        } else if (adminSettingsTab && adminSettingsTab.classList.contains('active')) {
            activeTab = 'admin';
        } else if (appearanceSettingsTab && appearanceSettingsTab.classList.contains('active')) {
            activeTab = 'appearance';
        } else if (connectionsSettingsTab && connectionsSettingsTab.classList.contains('active')) {
            activeTab = 'connections';
        } else {
            // Default to user tab if no active tab found
            activeTab = 'user';
            if (userSettingsTab) {
                userSettingsTab.classList.add('active', 'border-cyan-500', 'text-cyan-400');
                userSettingsTab.classList.remove('border-transparent', 'text-slate-400');
            }
        }

        // Hide all sections first
        if (userSettingsSection) userSettingsSection.classList.add('hidden');
        if (adminSettingsSection) adminSettingsSection.classList.add('hidden');
        if (appearanceSettingsSection) appearanceSettingsSection.classList.add('hidden');
        if (connectionsSettingsSection) connectionsSettingsSection.classList.add('hidden');

        // Show only the active section
        if (activeTab === 'user' && userSettingsSection) {
            userSettingsSection.classList.remove('hidden');
        } else if (activeTab === 'admin' && adminSettingsSection) {
            adminSettingsSection.classList.remove('hidden');
        } else if (activeTab === 'appearance' && appearanceSettingsSection) {
            appearanceSettingsSection.classList.remove('hidden');
        } else if (activeTab === 'connections' && connectionsSettingsSection) {
            connectionsSettingsSection.classList.remove('hidden');
        }

        console.log(`Fixed settings sections visibility, active tab: ${activeTab}`);
    }

    /**
     * Check if the current user is an admin
     * @returns {Promise<Object>} Object with admin status information
     */
    async checkAdminStatus() {
        try {
            const response = await fetch('/api/admin/check');
            const data = await response.json();

            // For backward compatibility, return true/false for direct boolean checks
            if (typeof data === 'object') {
                data.valueOf = function() { return this.is_admin === true; };
                data.toString = function() { return this.is_admin ? 'true' : 'false'; };
            }

            // Store admin status in the instance for future reference
            this.isUserAdmin = data.is_admin === true;
            this.isUserSuperAdmin = data.is_super_admin === true;

            // Update navigation items based on admin status
            this.updateAdminNavItems();

            return data;
        } catch (error) {
            console.error('Error checking admin status:', error);
            this.isUserAdmin = false;
            this.isUserSuperAdmin = false;
            return { is_admin: false, is_super_admin: false };
        }
    }

    /**
     * Update navigation items based on admin status
     */
    updateAdminNavItems() {
        // Show/hide statistics link based on admin status
        const statsLink = document.querySelector('.nav-item[data-view="statistics"]');
        if (statsLink) {
            if (this.isUserAdmin || this.isUserSuperAdmin) {
                statsLink.classList.remove('hidden');
            } else {
                statsLink.classList.add('hidden');
            }
        }
    }

    /**
     * Set up admin management functionality
     */
    setupAdminManagement() {
        const addAdminBtn = document.getElementById('addAdminBtn');
        const refreshAdminsBtn = document.getElementById('refreshAdminsBtn');
        const refreshActiveUsersBtn = document.getElementById('refreshActiveUsersBtn');
        const activeTimeFilter = document.getElementById('activeTimeFilter');
        const chatUsersTab = document.getElementById('chatUsersTab');
        const liveUsersTab = document.getElementById('liveUsersTab');
        const spotifyUsersTab = document.getElementById('spotifyUsersTab');

        // Admin management buttons
        if (addAdminBtn) {
            addAdminBtn.addEventListener('click', () => {
                this.showAddAdminModal();
            });
        }

        // Initialize admin credits component
        this.initializeAdminCredits();

        if (refreshAdminsBtn) {
            refreshAdminsBtn.addEventListener('click', () => {
                this.loadAdminList();
            });
        }

        // Active users buttons
        if (refreshActiveUsersBtn) {
            refreshActiveUsersBtn.addEventListener('click', () => {
                const minutes = activeTimeFilter ? activeTimeFilter.value : 15;
                this.loadActiveUsers(minutes);
            });
        }

        if (activeTimeFilter) {
            activeTimeFilter.addEventListener('change', () => {
                const minutes = activeTimeFilter.value;
                this.loadActiveUsers(minutes);
            });
        }

        // Active users tabs
        if (chatUsersTab && liveUsersTab && spotifyUsersTab) {
            const tabs = [chatUsersTab, liveUsersTab, spotifyUsersTab];
            const lists = [
                document.getElementById('chatUsersList'),
                document.getElementById('liveUsersList'),
                document.getElementById('spotifyUsersList')
            ];

            tabs.forEach((tab, index) => {
                tab.addEventListener('click', () => {
                    // Update tab styles
                    tabs.forEach(t => {
                        t.classList.remove('active');
                        t.classList.add('text-slate-400');
                        t.classList.remove('text-slate-200');
                        t.querySelector('span:last-child').classList.remove('scale-x-100');
                        t.querySelector('span:last-child').classList.add('scale-x-0');
                    });

                    tab.classList.add('active');
                    tab.classList.remove('text-slate-400');
                    tab.classList.add('text-slate-200');
                    tab.querySelector('span:last-child').classList.add('scale-x-100');
                    tab.querySelector('span:last-child').classList.remove('scale-x-0');

                    // Show corresponding list
                    lists.forEach(list => {
                        if (list) list.classList.add('hidden');
                    });

                    if (lists[index]) lists[index].classList.remove('hidden');
                });
            });
        }

        // Load data initially if in admin mode
        if (this.isAdminMode) {
            this.loadAdminList();
            this.loadActiveUsers(15);
        }
    }

    /**
     * Check if admin API requests should be allowed
     * @returns {boolean} Whether admin API requests should be allowed
     */
    canMakeAdminRequests() {
        // Only allow admin API requests if the user is an admin
        return this.isUserAdmin === true;
    }

    /**
     * Initialize admin credits component
     */
    initializeAdminCredits() {
        if (window.AdminCredits && this.isUserAdmin) {
            console.log('Initializing Admin Credits component...');
            window.adminCredits = new AdminCredits();
            window.adminCredits.init();
        }
    }

    /**
     * Load active users data
     * @param {number} minutes - Time window in minutes
     */
    async loadActiveUsers(minutes = 15) {
        try {
            // Helper function to safely update element innerHTML
            const safeSetInnerHTML = (id, html) => {
                const element = document.getElementById(id);
                if (element) element.innerHTML = html;
            };

            // Helper function to safely update element text content
            const safeSetTextContent = (id, text) => {
                const element = document.getElementById(id);
                if (element) element.textContent = text;
            };

            // Check if user can make admin requests
            if (!this.canMakeAdminRequests()) {
                // User is not an admin, show access denied message
                const errorMessage = '<div class="text-center text-amber-400 text-sm py-2">Admin access required to view this content</div>';
                safeSetInnerHTML('chatUsersList', errorMessage);
                safeSetInnerHTML('liveUsersList', errorMessage);
                safeSetInnerHTML('spotifyUsersList', errorMessage);
                safeSetInnerHTML('friendsUsersList', errorMessage);
                return;
            }

            // Update UI to show loading state
            const loadingHtml = '<div class="text-center text-slate-400 text-sm py-2">Loading users...</div>';

            // Set loading state
            safeSetInnerHTML('chatUsersList', loadingHtml);
            safeSetInnerHTML('liveUsersList', loadingHtml);
            safeSetInnerHTML('spotifyUsersList', loadingHtml);
            safeSetInnerHTML('friendsUsersList', loadingHtml);

            // Reset counters
            safeSetTextContent('chatUserCount', '0');
            safeSetTextContent('liveUserCount', '0');
            safeSetTextContent('spotifyUserCount', '0');
            safeSetTextContent('friendsUserCount', '0');
            safeSetTextContent('totalUniqueCount', '0');

            // Fetch active users data
            const response = await fetch(`/api/admin/active-users?minutes=${minutes}`);

            if (response.status === 403) {
                // User is not in admin mode or not an admin
                const errorMessage = '<div class="text-center text-amber-400 text-sm py-2">Admin mode must be enabled to view this content</div>';
                safeSetInnerHTML('chatUsersList', errorMessage);
                safeSetInnerHTML('liveUsersList', errorMessage);
                safeSetInnerHTML('spotifyUsersList', errorMessage);
                safeSetInnerHTML('friendsUsersList', errorMessage);
                return;
            }

            const data = await response.json();

            if (response.ok) {
                // Update counters
                safeSetTextContent('chatUserCount', data.chat.length);
                safeSetTextContent('liveUserCount', data.live.length);
                safeSetTextContent('spotifyUserCount', data.spotify.length);
                safeSetTextContent('friendsUserCount', data.friends.length);
                safeSetTextContent('totalUniqueCount', data.total_unique);

                // Update user lists
                this.updateActiveUsersList('chatUsersList', data.chat, 'chat');
                this.updateActiveUsersList('liveUsersList', data.live, 'live');
                this.updateActiveUsersList('spotifyUsersList', data.spotify, 'spotify');
                this.updateActiveUsersList('friendsUsersList', data.friends, 'friends');
            } else {
                // Show error
                const errorMessage = '<div class="text-center text-red-400 text-sm py-2">Failed to load active users</div>';
                safeSetInnerHTML('chatUsersList', errorMessage);
                safeSetInnerHTML('liveUsersList', errorMessage);
                safeSetInnerHTML('spotifyUsersList', errorMessage);
                safeSetInnerHTML('friendsUsersList', errorMessage);
            }
        } catch (error) {
            console.error('Error loading active users:', error);
            // Show error
            const errorMessage = '<div class="text-center text-red-400 text-sm py-2">Failed to load active users</div>';
            safeSetInnerHTML('chatUsersList', errorMessage);
            safeSetInnerHTML('liveUsersList', errorMessage);
            safeSetInnerHTML('spotifyUsersList', errorMessage);
            safeSetInnerHTML('friendsUsersList', errorMessage);
        }
    }

    /**
     * Update a specific active users list
     * @param {string} listId - ID of the list element
     * @param {Array} users - Array of user data
     * @param {string} section - Section name (chat, live, spotify, friends)
     */
    updateActiveUsersList(listId, users, section) {
        const listElement = document.getElementById(listId);
        if (!listElement) {
            console.log(`Element with ID '${listId}' not found, skipping update`);
            return;
        }

        if (users.length === 0) {
            listElement.innerHTML = `<div class="text-center text-slate-400 text-sm py-2">No active users in ${section}</div>`;
            return;
        }

        // Sort users by last active time (most recent first)
        users.sort((a, b) => new Date(b.last_active) - new Date(a.last_active));

        // Clear the list
        listElement.innerHTML = '';

        // Add users to the list
        users.forEach(activity => {
            // Get user data from the activity
            const user = activity.user || {};
            const username = user.username || activity.username || 'Unknown';
            const email = user.email || activity.email || 'Unknown';
            const profilePicture = user.profile_picture || activity.profile_picture;

            const userElement = document.createElement('div');
            userElement.className = 'bg-slate-700/30 rounded-lg p-3 border border-slate-600/50 flex items-center justify-between';

            // Format the last active time
            const lastActive = new Date(activity.last_active);
            const now = new Date();
            const diffMs = now - lastActive;
            const diffMins = Math.floor(diffMs / 60000);
            const diffHours = Math.floor(diffMins / 60);

            let timeString;
            if (diffMins < 1) {
                timeString = 'Just now';
            } else if (diffMins < 60) {
                timeString = `${diffMins} min${diffMins === 1 ? '' : 's'} ago`;
            } else {
                timeString = `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;
            }

            // Set icon based on section
            let iconName = 'message-square';
            let iconColor = 'text-cyan-400';
            let bgColor = 'bg-cyan-500/20';

            if (section === 'live') {
                iconName = 'users';
                iconColor = 'text-green-400';
                bgColor = 'bg-green-500/20';
            } else if (section === 'spotify') {
                iconName = 'music';
                iconColor = 'text-purple-400';
                bgColor = 'bg-purple-500/20';
            } else if (section === 'friends') {
                iconName = 'users';
                iconColor = 'text-blue-400';
                bgColor = 'bg-blue-500/20';
            }

            // Create user element HTML
            userElement.innerHTML = `
                <div class="flex items-center space-x-3">
                    ${profilePicture ?
                        `<img src="${profilePicture}" alt="${username}" class="w-8 h-8 rounded-full object-cover">` :
                        `<div class="w-8 h-8 rounded-full ${bgColor} flex items-center justify-center ${iconColor}">
                            <i data-lucide="${iconName}" class="h-4 w-4"></i>
                        </div>`
                    }
                    <div>
                        <div class="text-sm font-medium text-slate-200">${username}</div>
                        <div class="text-xs text-slate-400">${email}</div>
                    </div>
                </div>
                <div>
                    <span class="text-xs bg-slate-800/50 text-slate-300 px-2 py-0.5 rounded">${timeString}</span>
                </div>
            `;

            listElement.appendChild(userElement);
        });

        // Initialize icons
        if (window.lucide) {
            lucide.createIcons();
        }
    }

    /**
     * Load the list of admins
     */
    async loadAdminList() {
        try {
            const adminList = document.getElementById('adminList');
            if (!adminList) return;

            // Check if user can make admin requests
            if (!this.canMakeAdminRequests()) {
                // User is not an admin, show access denied message
                adminList.innerHTML = `
                    <div class="bg-amber-900/20 rounded-lg p-3 border border-amber-700/50 text-center">
                        <p class="text-sm text-amber-400">Admin access required to view this content</p>
                    </div>
                `;
                return;
            }

            // Show loading state
            adminList.innerHTML = `
                <div class="flex items-center justify-center p-4">
                    <i data-lucide="loader" class="h-6 w-6 text-slate-400 animate-spin"></i>
                    <span class="ml-2 text-slate-400">Loading admins...</span>
                </div>
            `;

            // Initialize icons
            if (window.lucide) lucide.createIcons();

            // Check if user is a super admin
            const isSuperAdmin = this.isUserSuperAdmin === true;

            // Fetch admin list
            const response = await fetch('/api/admin/list-admins');

            if (response.status === 403) {
                // User is not in admin mode or not an admin
                adminList.innerHTML = `
                    <div class="bg-amber-900/20 rounded-lg p-3 border border-amber-700/50 text-center">
                        <p class="text-sm text-amber-400">Admin mode must be enabled to view this content</p>
                    </div>
                `;
                return;
            }

            const admins = await response.json();

            if (response.ok) {
                // Clear loading state
                adminList.innerHTML = '';

                // Add primary admins (always first)
                const primaryAdmins = admins.filter(admin => admin.is_primary);
                primaryAdmins.forEach(primaryAdmin => {
                    const primaryAdminElement = document.createElement('div');
                    primaryAdminElement.className = 'bg-slate-700/30 rounded-lg p-3 border border-slate-600/50';

                    // Determine badge text
                    let badgeText = 'Primary Admin';
                    if (primaryAdmin.is_super_admin) {
                        badgeText = 'Super Admin';
                    }

                    // Create HTML content
                    let adminHTML = `
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center text-red-400">
                                    <i data-lucide="shield" class="h-4 w-4"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-slate-200">${primaryAdmin.email}</div>
                                    <div class="text-xs text-slate-400">${badgeText}</div>
                                </div>
                            </div>
                            <div>
                                <span class="text-xs bg-red-500/20 text-red-400 px-2 py-0.5 rounded">Cannot Remove</span>
                            </div>
                        </div>
                    `;

                    // Add contact visibility toggle for super admins
                    if (isSuperAdmin) {
                        adminHTML += `
                            <div class="mt-2 pt-2 border-t border-slate-600/30 flex items-center justify-between">
                                <span class="text-xs text-slate-400">Show on contacts page</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer contact-visibility-toggle"
                                        data-email="${primaryAdmin.email}"
                                        ${primaryAdmin.show_on_contacts ? 'checked' : ''}>
                                    <div class="w-9 h-5 bg-slate-700 peer-focus:outline-none rounded-full peer
                                        peer-checked:after:translate-x-full peer-checked:after:border-white
                                        after:content-[''] after:absolute after:top-[2px] after:left-[2px]
                                        after:bg-slate-400 after:border-slate-300 after:border after:rounded-full
                                        after:h-4 after:w-4 after:transition-all peer-checked:bg-cyan-600
                                        peer-checked:after:bg-white"></div>
                                </label>
                            </div>
                        `;
                    }

                    primaryAdminElement.innerHTML = adminHTML;
                    adminList.appendChild(primaryAdminElement);
                });

                // Add other admins
                admins.filter(admin => !admin.is_primary).forEach(admin => {
                    const adminElement = document.createElement('div');
                    adminElement.className = 'bg-slate-700/30 rounded-lg p-3 border border-slate-600/50';

                    // Determine icon and badge based on admin type
                    let iconClass = 'bg-cyan-500/20 text-cyan-400';
                    let iconName = 'user-check';
                    let adminType = 'Admin';

                    if (admin.is_super_admin) {
                        iconClass = 'bg-purple-500/20 text-purple-400';
                        iconName = 'shield';
                        adminType = 'Super Admin';
                    }

                    // Create HTML content
                    let adminHTML = `
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 rounded-full ${iconClass} flex items-center justify-center">
                                    <i data-lucide="${iconName}" class="h-4 w-4"></i>
                                </div>
                                <div>
                                    <div class="text-sm font-medium text-slate-200">${admin.email}</div>
                                    <div class="text-xs text-slate-400">${adminType}</div>
                                </div>
                            </div>
                            <div>
                                <button class="text-xs bg-red-900/30 hover:bg-red-900/50 text-red-400 px-2 py-1 rounded flex items-center remove-admin-btn" data-email="${admin.email}">
                                    <i data-lucide="user-minus" class="h-3 w-3 mr-1"></i>
                                    Remove
                                </button>
                            </div>
                        </div>
                    `;

                    // Add contact visibility toggle for super admins
                    if (isSuperAdmin) {
                        adminHTML += `
                            <div class="mt-2 pt-2 border-t border-slate-600/30 flex items-center justify-between">
                                <span class="text-xs text-slate-400">Show on contacts page</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer contact-visibility-toggle"
                                        data-email="${admin.email}"
                                        ${admin.show_on_contacts ? 'checked' : ''}>
                                    <div class="w-9 h-5 bg-slate-700 peer-focus:outline-none rounded-full peer
                                        peer-checked:after:translate-x-full peer-checked:after:border-white
                                        after:content-[''] after:absolute after:top-[2px] after:left-[2px]
                                        after:bg-slate-400 after:border-slate-300 after:border after:rounded-full
                                        after:h-4 after:w-4 after:transition-all peer-checked:bg-cyan-600
                                        peer-checked:after:bg-white"></div>
                                </label>
                            </div>
                        `;
                    }

                    adminElement.innerHTML = adminHTML;
                    adminList.appendChild(adminElement);
                });

                // Initialize icons
                if (window.lucide) lucide.createIcons();

                // Add event listeners to remove buttons
                document.querySelectorAll('.remove-admin-btn').forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        const email = e.currentTarget.getAttribute('data-email');
                        this.showRemoveAdminModal(email);
                    });
                });

                // Add event listeners to contact visibility toggles
                if (isSuperAdmin) {
                    document.querySelectorAll('.contact-visibility-toggle').forEach(toggle => {
                        toggle.addEventListener('change', (e) => {
                            const email = e.currentTarget.getAttribute('data-email');
                            const showOnContacts = e.currentTarget.checked;
                            this.toggleAdminContactVisibility(email, showOnContacts);
                        });
                    });
                }

                // Show message if no other admins
                if (admins.filter(admin => !admin.is_primary).length === 0) {
                    const noAdminsElement = document.createElement('div');
                    noAdminsElement.className = 'bg-slate-700/30 rounded-lg p-3 border border-slate-600/50 text-center';
                    noAdminsElement.innerHTML = `
                        <p class="text-sm text-slate-400">No additional admins</p>
                    `;
                    adminList.appendChild(noAdminsElement);
                }
            } else {
                // Show error
                adminList.innerHTML = `
                    <div class="bg-red-900/20 rounded-lg p-3 border border-red-700/50 text-center">
                        <p class="text-sm text-red-400">Failed to load admins</p>
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error loading admin list:', error);
            const adminList = document.getElementById('adminList');
            if (adminList) {
                adminList.innerHTML = `
                    <div class="bg-red-900/20 rounded-lg p-3 border border-red-700/50 text-center">
                        <p class="text-sm text-red-400">Failed to load admins</p>
                    </div>
                `;
            }
        }
    }

    /**
     * Toggle admin visibility on the contact page
     * @param {string} email - Admin email
     * @param {boolean} showOnContacts - Whether to show the admin on the contact page
     */
    async toggleAdminContactVisibility(email, showOnContacts) {
        try {
            // Check if user can make admin requests and is a super admin
            if (!this.canMakeAdminRequests() || !this.isUserSuperAdmin) {
                this.showToast('Super admin access required to perform this action', 'error');

                // Reset the toggle to its previous state
                const toggle = document.querySelector(`.contact-visibility-toggle[data-email="${email}"]`);
                if (toggle) {
                    toggle.checked = !showOnContacts;
                }
                return;
            }

            // Call the API to toggle visibility
            const response = await fetch('/api/admin/toggle-contact-visibility', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: email,
                    show_on_contacts: showOnContacts
                })
            });

            const data = await response.json();

            if (response.ok) {
                this.showToast(`Admin ${email} ${showOnContacts ? 'will' : 'will not'} be shown on contacts page`);

                // Reload contacts to reflect changes
                this.loadContacts();
            } else {
                this.showToast(data.error || 'Failed to update contact visibility', 'error');

                // Reset the toggle to its previous state
                const toggle = document.querySelector(`.contact-visibility-toggle[data-email="${email}"]`);
                if (toggle) {
                    toggle.checked = !showOnContacts;
                }
            }
        } catch (error) {
            console.error('Error toggling admin contact visibility:', error);
            this.showToast('Failed to update contact visibility', 'error');

            // Reset the toggle to its previous state
            const toggle = document.querySelector(`.contact-visibility-toggle[data-email="${email}"]`);
            if (toggle) {
                toggle.checked = !showOnContacts;
            }
        }
    }

    /**
     * Show modal to add a new admin
     */
    showAddAdminModal() {
        // Check if user can make admin requests
        if (!this.canMakeAdminRequests()) {
            this.showToast('Admin access required to perform this action', 'error');
            return;
        }

        // Create modal
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black/70 flex items-center justify-center z-50';
        modal.id = 'addAdminModal';

        modal.innerHTML = `
            <div class="bg-slate-800 rounded-lg p-6 w-96 max-w-full">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-slate-100">Add Admin</h3>
                    <button id="closeAddAdminModal" class="text-slate-400 hover:text-slate-100">
                        <i data-lucide="x" class="h-5 w-5"></i>
                    </button>
                </div>
                <div id="addAdminStep1" class="space-y-4">
                    <p class="text-slate-300 text-sm">Enter the email of the user you want to make an admin.</p>
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-1">Email</label>
                        <input type="email" id="adminEmail" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500" required>
                    </div>
                    <div class="pt-2">
                        <button type="button" id="requestAdminVerificationBtn" class="w-full bg-cyan-600 hover:bg-cyan-500 text-white rounded-md px-4 py-2 flex items-center justify-center">
                            <i data-lucide="mail" class="h-4 w-4 mr-2"></i>
                            Send Verification Code
                        </button>
                    </div>
                </div>
                <div id="addAdminStep2" class="space-y-4 hidden">
                    <p class="text-slate-300 text-sm">Enter the verification code sent to your email.</p>
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-1">Verification Code</label>
                        <input type="text" id="adminVerificationCode" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500" required>
                    </div>
                    <div class="pt-2">
                        <button type="button" id="confirmAddAdminBtn" class="w-full bg-cyan-600 hover:bg-cyan-500 text-white rounded-md px-4 py-2 flex items-center justify-center">
                            <i data-lucide="user-plus" class="h-4 w-4 mr-2"></i>
                            Add Admin
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Refresh icons
        if (window.lucide) {
            lucide.createIcons();
        }

        // Add event listeners
        const closeButton = document.getElementById('closeAddAdminModal');
        if (closeButton) {
            closeButton.addEventListener('click', () => {
                modal.remove();
            });
        }

        // Request verification code button
        const requestVerificationBtn = document.getElementById('requestAdminVerificationBtn');
        if (requestVerificationBtn) {
            requestVerificationBtn.addEventListener('click', () => {
                const email = document.getElementById('adminEmail').value;
                if (!email) {
                    this.showToast('Please enter an email', 'error');
                    return;
                }

                // Show loading state
                const originalButtonText = requestVerificationBtn.innerHTML;
                requestVerificationBtn.innerHTML = `<i data-lucide="loader" class="h-4 w-4 mr-2 animate-spin"></i>Sending Code...`;
                requestVerificationBtn.disabled = true;

                // Initialize icons
                if (window.lucide) {
                    lucide.createIcons({
                        attrs: {
                            class: ["h-4", "w-4"]
                        },
                        elements: [requestVerificationBtn]
                    });
                }

                // Call the API to request verification
                fetch('/api/admin/request-verification', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'add',
                        email: email
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        this.showToast(data.error, 'error');
                        requestVerificationBtn.innerHTML = originalButtonText;
                        requestVerificationBtn.disabled = false;
                        if (window.lucide) lucide.createIcons();
                    } else {
                        this.showToast(data.message || 'Verification code sent successfully');

                        // Show step 2
                        document.getElementById('addAdminStep1').classList.add('hidden');
                        document.getElementById('addAdminStep2').classList.remove('hidden');
                    }
                })
                .catch(error => {
                    console.error('Error requesting verification:', error);
                    this.showToast('Failed to send verification code. Please try again.', 'error');
                    requestVerificationBtn.innerHTML = originalButtonText;
                    requestVerificationBtn.disabled = false;
                    if (window.lucide) lucide.createIcons();
                });
            });
        }

        // Confirm add admin button
        const confirmAddAdminBtn = document.getElementById('confirmAddAdminBtn');
        if (confirmAddAdminBtn) {
            confirmAddAdminBtn.addEventListener('click', () => {
                const email = document.getElementById('adminEmail').value;
                const verificationCode = document.getElementById('adminVerificationCode').value;

                if (!verificationCode) {
                    this.showToast('Please enter the verification code', 'error');
                    return;
                }

                // Show loading state
                const originalButtonText = confirmAddAdminBtn.innerHTML;
                confirmAddAdminBtn.innerHTML = `<i data-lucide="loader" class="h-4 w-4 mr-2 animate-spin"></i>Adding Admin...`;
                confirmAddAdminBtn.disabled = true;

                // Initialize icons
                if (window.lucide) {
                    lucide.createIcons({
                        attrs: {
                            class: ["h-4", "w-4"]
                        },
                        elements: [confirmAddAdminBtn]
                    });
                }

                // Call the API to add admin
                fetch('/api/admin/add-admin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        verification_code: verificationCode
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        this.showToast(data.error, 'error');
                        confirmAddAdminBtn.innerHTML = originalButtonText;
                        confirmAddAdminBtn.disabled = false;
                        if (window.lucide) lucide.createIcons();
                    } else {
                        this.showToast(data.message || 'Admin added successfully');
                        modal.remove();

                        // Reload admin list
                        this.loadAdminList();
                    }
                })
                .catch(error => {
                    console.error('Error adding admin:', error);
                    this.showToast('Failed to add admin. Please try again.', 'error');
                    confirmAddAdminBtn.innerHTML = originalButtonText;
                    confirmAddAdminBtn.disabled = false;
                    if (window.lucide) lucide.createIcons();
                });
            });
        }
    }

    /**
     * Show modal to remove an admin
     * @param {string} email - The email of the admin to remove
     */
    showRemoveAdminModal(email) {
        // Check if user can make admin requests
        if (!this.canMakeAdminRequests()) {
            this.showToast('Admin access required to perform this action', 'error');
            return;
        }

        // Create modal
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black/70 flex items-center justify-center z-50';
        modal.id = 'removeAdminModal';

        modal.innerHTML = `
            <div class="bg-slate-800 rounded-lg p-6 w-96 max-w-full">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-slate-100">Remove Admin</h3>
                    <button id="closeRemoveAdminModal" class="text-slate-400 hover:text-slate-100">
                        <i data-lucide="x" class="h-5 w-5"></i>
                    </button>
                </div>
                <div id="removeAdminStep1" class="space-y-4">
                    <p class="text-slate-300 text-sm">Are you sure you want to remove <strong>${email}</strong> as an admin?</p>
                    <div class="pt-2">
                        <button type="button" id="requestRemoveVerificationBtn" class="w-full bg-red-600 hover:bg-red-500 text-white rounded-md px-4 py-2 flex items-center justify-center">
                            <i data-lucide="mail" class="h-4 w-4 mr-2"></i>
                            Send Verification Code
                        </button>
                    </div>
                </div>
                <div id="removeAdminStep2" class="space-y-4 hidden">
                    <p class="text-slate-300 text-sm">Enter the verification code sent to your email.</p>
                    <div>
                        <label class="block text-sm font-medium text-slate-300 mb-1">Verification Code</label>
                        <input type="text" id="removeVerificationCode" class="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-slate-200 focus:outline-none focus:ring-2 focus:ring-cyan-500" required>
                    </div>
                    <div class="pt-2">
                        <button type="button" id="confirmRemoveAdminBtn" class="w-full bg-red-600 hover:bg-red-500 text-white rounded-md px-4 py-2 flex items-center justify-center">
                            <i data-lucide="user-minus" class="h-4 w-4 mr-2"></i>
                            Remove Admin
                        </button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Refresh icons
        if (window.lucide) {
            lucide.createIcons();
        }

        // Add event listeners
        const closeButton = document.getElementById('closeRemoveAdminModal');
        if (closeButton) {
            closeButton.addEventListener('click', () => {
                modal.remove();
            });
        }

        // Request verification code button
        const requestVerificationBtn = document.getElementById('requestRemoveVerificationBtn');
        if (requestVerificationBtn) {
            requestVerificationBtn.addEventListener('click', () => {
                // Show loading state
                const originalButtonText = requestVerificationBtn.innerHTML;
                requestVerificationBtn.innerHTML = `<i data-lucide="loader" class="h-4 w-4 mr-2 animate-spin"></i>Sending Code...`;
                requestVerificationBtn.disabled = true;

                // Initialize icons
                if (window.lucide) {
                    lucide.createIcons({
                        attrs: {
                            class: ["h-4", "w-4"]
                        },
                        elements: [requestVerificationBtn]
                    });
                }

                // Call the API to request verification
                fetch('/api/admin/request-verification', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'remove',
                        email: email
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        this.showToast(data.error, 'error');
                        requestVerificationBtn.innerHTML = originalButtonText;
                        requestVerificationBtn.disabled = false;
                        if (window.lucide) lucide.createIcons();
                    } else {
                        this.showToast(data.message || 'Verification code sent successfully');

                        // Show step 2
                        document.getElementById('removeAdminStep1').classList.add('hidden');
                        document.getElementById('removeAdminStep2').classList.remove('hidden');
                    }
                })
                .catch(error => {
                    console.error('Error requesting verification:', error);
                    this.showToast('Failed to send verification code. Please try again.', 'error');
                    requestVerificationBtn.innerHTML = originalButtonText;
                    requestVerificationBtn.disabled = false;
                    if (window.lucide) lucide.createIcons();
                });
            });
        }

        // Confirm remove admin button
        const confirmRemoveAdminBtn = document.getElementById('confirmRemoveAdminBtn');
        if (confirmRemoveAdminBtn) {
            confirmRemoveAdminBtn.addEventListener('click', () => {
                const verificationCode = document.getElementById('removeVerificationCode').value;

                if (!verificationCode) {
                    this.showToast('Please enter the verification code', 'error');
                    return;
                }

                // Show loading state
                const originalButtonText = confirmRemoveAdminBtn.innerHTML;
                confirmRemoveAdminBtn.innerHTML = `<i data-lucide="loader" class="h-4 w-4 mr-2 animate-spin"></i>Removing Admin...`;
                confirmRemoveAdminBtn.disabled = true;

                // Initialize icons
                if (window.lucide) {
                    lucide.createIcons({
                        attrs: {
                            class: ["h-4", "w-4"]
                        },
                        elements: [confirmRemoveAdminBtn]
                    });
                }

                // Call the API to remove admin
                fetch('/api/admin/remove-admin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: email,
                        verification_code: verificationCode
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        this.showToast(data.error, 'error');
                        confirmRemoveAdminBtn.innerHTML = originalButtonText;
                        confirmRemoveAdminBtn.disabled = false;
                        if (window.lucide) lucide.createIcons();
                    } else {
                        this.showToast(data.message || 'Admin removed successfully');
                        modal.remove();

                        // Reload admin list
                        this.loadAdminList();
                    }
                })
                .catch(error => {
                    console.error('Error removing admin:', error);
                    this.showToast('Failed to remove admin. Please try again.', 'error');
                    confirmRemoveAdminBtn.innerHTML = originalButtonText;
                    confirmRemoveAdminBtn.disabled = false;
                    if (window.lucide) lucide.createIcons();
                });
            });
        }
    }

    /**
     * Show a toast notification
     * @param {string} message Message to display
     * @param {string} type Type of toast (success, error, warning, info)
     * @param {number} duration Duration in milliseconds
     */
    showToast(message, type = 'success', duration = 3000) {
        // Create toast container if it doesn't exist
        let toastContainer = document.getElementById('toastContainer');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toastContainer';
            toastContainer.className = 'fixed bottom-4 right-4 z-50 flex flex-col space-y-2';
            document.body.appendChild(toastContainer);
        }

        // Create toast element
        const toast = document.createElement('div');

        // Set toast classes based on type
        let bgColor, textColor, icon;
        switch (type) {
            case 'error':
                bgColor = 'bg-red-900/80';
                textColor = 'text-red-200';
                icon = 'alert-circle';
                break;
            case 'warning':
                bgColor = 'bg-amber-900/80';
                textColor = 'text-amber-200';
                icon = 'alert-triangle';
                break;
            case 'info':
                bgColor = 'bg-blue-900/80';
                textColor = 'text-blue-200';
                icon = 'info';
                break;
            default: // success
                bgColor = 'bg-green-900/80';
                textColor = 'text-green-200';
                icon = 'check-circle';
        }

        toast.className = `${bgColor} ${textColor} px-4 py-3 rounded-lg shadow-lg flex items-center min-w-[300px] max-w-md transform transition-all duration-300 ease-in-out translate-x-0 opacity-0`;
        toast.innerHTML = `
            <i data-lucide="${icon}" class="h-5 w-5 mr-2"></i>
            <span class="flex-grow">${message}</span>
            <button class="ml-2 text-slate-300 hover:text-white">
                <i data-lucide="x" class="h-4 w-4"></i>
            </button>
        `;

        // Add to container
        toastContainer.appendChild(toast);

        // Initialize icons
        if (window.lucide) {
            lucide.createIcons();
        }

        // Add close button event
        const closeBtn = toast.querySelector('button');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                toast.classList.remove('opacity-100');
                toast.classList.add('opacity-0', 'translate-x-full');
                setTimeout(() => {
                    toast.remove();
                }, 300);
            });
        }

        // Show toast with animation
        setTimeout(() => {
            toast.classList.add('opacity-100');
        }, 10);

        // Auto-remove after duration
        setTimeout(() => {
            if (toast.parentNode) {
                toast.classList.remove('opacity-100');
                toast.classList.add('opacity-0', 'translate-x-full');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.remove();
                    }
                }, 300);
            }
        }, duration);
    }

    /**
     * Update UI based on admin mode
     */
    updateUIForAdminMode() {
        // Get all admin-only elements
        const adminElements = document.querySelectorAll('.admin-only');

        // If user is not an admin, always hide admin elements regardless of admin mode
        if (!this.isUserAdmin) {
            adminElements.forEach(element => {
                element.classList.add('hidden');
            });

            // Hide admin settings tab completely for non-admin users
            const adminSettingsTab = document.getElementById('adminSettingsTab');
            if (adminSettingsTab) {
                adminSettingsTab.classList.add('hidden');

                // If admin tab is active but user is not an admin, switch to user tab
                const userSettingsTab = document.getElementById('userSettingsTab');
                const userSettingsSection = document.getElementById('userSettingsSection');
                const adminSettingsSection = document.getElementById('adminSettingsSection');

                if (userSettingsTab && userSettingsSection && adminSettingsSection) {
                    if (adminSettingsSection.classList.contains('hidden') === false) {
                        // Update active tab
                        this.activateSettingsTab(userSettingsTab);
                        this.deactivateSettingsTab(adminSettingsTab);

                        // Show user settings, hide admin settings
                        userSettingsSection.classList.remove('hidden');
                        adminSettingsSection.classList.add('hidden');
                    }
                }
            }

            // Hide admin link in user menu for non-admin users
            const adminLink = document.getElementById('adminLink');
            if (adminLink) {
                adminLink.classList.add('hidden');
            }

            console.log('Admin elements hidden for non-admin user');
            return; // Exit early for non-admin users
        }

        // For admin users, continue with normal admin mode toggle functionality
        // Show/hide admin elements based on admin mode
        adminElements.forEach(element => {
            if (this.isAdminMode) {
                element.classList.remove('hidden');
            } else {
                element.classList.add('hidden');
            }
        });

        // Update admin link styling in user menu
        const adminLink = document.getElementById('adminLink');
        if (adminLink && this.isAdminMode) {
            // Make admin link visible for admin users
            adminLink.classList.remove('hidden');

            // Optional: Add a visual indicator that admin mode is active
            adminLink.classList.add('text-cyan-400');
        }

        // Update user menu
        const userMenuTrigger = document.getElementById('userMenuTrigger');
        if (userMenuTrigger) {
            const userInitials = userMenuTrigger.querySelector('div');
            if (userInitials) {
                // Check if using profile picture (has img element)
                const profileImg = userInitials.querySelector('img');

                if (profileImg) {
                    // Using profile picture - just toggle admin indicator
                    if (this.isAdminMode) {
                        // Add admin indicator (red border)
                        profileImg.classList.add('border-2', 'border-red-600');
                    } else {
                        // Remove admin indicator
                        profileImg.classList.remove('border-2', 'border-red-600');
                    }
                } else {
                    // Using initials with background color
                    // Store the current background color classes
                    const bgClasses = Array.from(userInitials.classList)
                        .filter(cls => cls.startsWith('bg-') && cls !== 'bg-red-900');

                    if (this.isAdminMode) {
                        // Remove all background colors except admin color
                        bgClasses.forEach(cls => userInitials.classList.remove(cls));
                        userInitials.classList.add('bg-red-900');
                    } else {
                        // Remove admin color
                        userInitials.classList.remove('bg-red-900');

                        // If no background color is present, fetch user data again
                        const hasBgColor = Array.from(userInitials.classList)
                            .some(cls => cls.startsWith('bg-'));

                        if (!hasBgColor) {
                            this.fetchCurrentUser();
                        }
                    }
                }
            }
        }

        // Update user name in menu
        const userName = document.querySelector('#userMenu .text-sm.font-medium');
        if (userName) {
            userName.textContent = this.isAdminMode ? 'Admin' : 'Kevko';
        }

        // Update settings tabs
        const adminSettingsTab = document.getElementById('adminSettingsTab');
        if (adminSettingsTab) {
            if (this.isAdminMode) {
                adminSettingsTab.classList.remove('hidden');

                // Load admin data when admin mode is enabled
                this.loadAdminList();
                this.loadActiveUsers(15);
            } else {
                adminSettingsTab.classList.add('hidden');

                // If admin tab is active but admin mode is disabled, switch to user tab
                const userSettingsTab = document.getElementById('userSettingsTab');
                const userSettingsSection = document.getElementById('userSettingsSection');
                const adminSettingsSection = document.getElementById('adminSettingsSection');

                if (userSettingsTab && userSettingsSection && adminSettingsSection) {
                    if (adminSettingsSection.classList.contains('hidden') === false) {
                        // Update active tab
                        this.activateSettingsTab(userSettingsTab);
                        this.deactivateSettingsTab(adminSettingsTab);

                        // Show user settings, hide admin settings
                        userSettingsSection.classList.remove('hidden');
                        adminSettingsSection.classList.add('hidden');
                    }
                }
            }
        }

        console.log(`Admin mode ${this.isAdminMode ? 'enabled' : 'disabled'}`);
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    try {
        console.log('DOM loaded, initializing dashboard...');
        const dashboard = new Dashboard();
        await dashboard.init();

        // Make dashboard globally accessible for debugging
        window.dashboard = dashboard;
        console.log('Dashboard initialization complete');

        // Check if services were loaded correctly
        const serviceCount = dashboard.serviceManager.services.size;
        console.log(`Dashboard initialized with ${serviceCount} services`);

        // Log the current service configurations in localStorage
        const storedConfigs = localStorage.getItem('serviceConfigurations');
        console.log('Current service configurations in localStorage:', storedConfigs);
    } catch (error) {
        console.error('Error initializing dashboard:', error);
    }
});
